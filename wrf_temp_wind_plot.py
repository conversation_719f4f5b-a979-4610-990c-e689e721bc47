#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WRF温度和风速矢量可视化工具
用于绘制地面2米温度场和10米风速矢量叠加图

主要功能:
- 读取WRF数据中的温度和风场数据
- 绘制两个子图对比不同文件
- 在温度场上叠加风速矢量箭头
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import matplotlib.colors as colors
from netCDF4 import Dataset
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings("ignore")

# 设置字体支持中文显示
plt.rcParams.update(
    {
        "font.family": ["Times New Roman", "SimHei"],
        "mathtext.fontset": "stix",
        "axes.unicode_minus": False,
        "figure.max_open_warning": 0,
    }
)


class WRFTempWindVisualizer:
    """WRF温度和风场可视化类"""

    def __init__(self, config=None):
        """初始化可视化器"""
        self.config = config or self.get_default_config()

    def get_default_config(self):
        """获取默认配置"""
        return {
            "figure_size": (16, 8),
            "dpi": 800,
            "temp_cmap": "RdYlBu_r",  # 温度场色标
            "wind_arrow_step": 8,  # 风矢量稀疏步长
            "wind_arrow_scale": 15,  # 风矢量比例（减小值使箭头更长）
            "arrow_width": 0.0025,  # 箭头宽度
            "arrow_headwidth": 4.0,  # 箭头头部宽度
            "arrow_headlength": 4.0,  # 箭头头部长度
            "arrow_color": "black",  # 箭头颜色
            "arrow_alpha": 0.7,  # 箭头透明度
            "contour_levels": 15,  # 温度等值线数量
            "enable_contour_lines": True,  # 是否绘制等值线
            "enable_grid": True,  # 是否显示网格
            "apply_bounds": True,  # 是否应用边界限制
            "plot_bounds": {  # 绘图边界
                "min_lon": 103.934,
                "max_lon": 104.12,
                "min_lat": 32.92,
                "max_lat": 33.07,
            },
            # 气象站点信息
            "stations": [
                {"name": "30M", "lon": 104.055, "lat": 32.9887},
                {"name": "75M", "lon": 104.016, "lat": 32.9998},
            ],
            "station_color": "red",
            "station_size": 150,  # 增大站点标记大小
            # 字体大小设置
            "label_fontsize": 14,  # 坐标轴标签字体
            "title_fontsize": 16,  # 标题字体
            "tick_fontsize": 12,  # 刻度字体
            "station_fontsize": 12,  # 站点标签字体
            "stats_fontsize": 11,  # 统计信息字体
            "colorbar_fontsize": 12,  # 色标字体
            "contour_fontsize": 10,  # 等值线标注字体
        }

    def read_wrf_data(self, filename, time_idx=0):
        """
        读取WRF数据文件（不依赖wrf-python）

        Args:
            filename: WRF文件路径
            time_idx: 时间索引

        Returns:
            包含温度、风场等数据的字典
        """
        try:
            print(f"正在读取文件: {filename}")
            ncfile = Dataset(filename)

            # 读取2米温度 (T2) - 直接读取NetCDF变量
            t2 = ncfile.variables["T2"][time_idx, :, :]
            t2 = np.array(t2) - 273.15  # 转换为摄氏度

            # 读取10米风场 (U10, V10)
            u10 = ncfile.variables["U10"][time_idx, :, :]
            v10 = ncfile.variables["V10"][time_idx, :, :]
            u10 = np.array(u10)
            v10 = np.array(v10)

            # 计算风速
            wspd10 = np.sqrt(u10**2 + v10**2)

            # 获取经纬度坐标
            if "XLAT" in ncfile.variables and "XLONG" in ncfile.variables:
                lats = ncfile.variables["XLAT"][time_idx, :, :]
                lons = ncfile.variables["XLONG"][time_idx, :, :]
            elif "XLAT_M" in ncfile.variables and "XLONG_M" in ncfile.variables:
                lats = ncfile.variables["XLAT_M"][time_idx, :, :]
                lons = ncfile.variables["XLONG_M"][time_idx, :, :]
            else:
                # 如果没有直接的经纬度，尝试从其他变量推导
                ny, nx = t2.shape
                lats = np.zeros((ny, nx))
                lons = np.zeros((ny, nx))
                print("警告：未找到经纬度坐标，使用默认值")

            lats = np.array(lats)
            lons = np.array(lons)

            # 获取时间信息
            try:
                times = ncfile.variables["Times"][time_idx]
                time_str = "".join(
                    [
                        t.decode("utf-8") if isinstance(t, bytes) else str(t)
                        for t in times
                    ]
                )
                # 解析UTC时间并转换为北京时间
                try:
                    # WRF时间格式通常是 'YYYY-MM-DD_HH:MM:SS'
                    utc_time = datetime.strptime(time_str.strip(), "%Y-%m-%d_%H:%M:%S")
                    beijing_time = utc_time + timedelta(hours=8)
                    time_str = (
                        beijing_time.strftime("%Y-%m-%d %H:%M:%S") + " (北京时间)"
                    )
                except:
                    time_str = time_str + " (UTC)"
            except:
                time_str = f"Time Index: {time_idx}"

            ncfile.close()

            print(f"  - 温度范围: {t2.min():.1f}°C 到 {t2.max():.1f}°C")
            print(f"  - 风速范围: {wspd10.min():.1f} 到 {wspd10.max():.1f} m/s")
            print(f"  - 数据维度: {t2.shape}")
            print(f"  - 时间: {time_str}")

            return {
                "temperature": t2,
                "u10": u10,
                "v10": v10,
                "wind_speed": wspd10,
                "lat": lats,
                "lon": lons,
                "time": time_str,
                "filename": os.path.basename(filename),
            }

        except Exception as e:
            print(f"读取WRF数据时出错: {e}")
            if "ncfile" in locals():
                ncfile.close()
            return None

    def apply_plot_bounds(self, data):
        """应用绘图边界限制"""
        if not self.config["apply_bounds"]:
            return data

        bounds = self.config["plot_bounds"]
        lon = data["lon"]
        lat = data["lat"]

        # 创建掩码
        lon_mask = (lon >= bounds["min_lon"]) & (lon <= bounds["max_lon"])
        lat_mask = (lat >= bounds["min_lat"]) & (lat <= bounds["max_lat"])
        mask = lon_mask & lat_mask

        # 找到有效区域的边界
        valid_rows, valid_cols = np.where(mask)
        if len(valid_rows) == 0:
            print("指定范围内没有有效数据点")
            return data

        row_min, row_max = valid_rows.min(), valid_rows.max() + 1
        col_min, col_max = valid_cols.min(), valid_cols.max() + 1

        # 裁剪所有数组
        bounded_data = {
            "temperature": data["temperature"][row_min:row_max, col_min:col_max],
            "u10": data["u10"][row_min:row_max, col_min:col_max],
            "v10": data["v10"][row_min:row_max, col_min:col_max],
            "wind_speed": data["wind_speed"][row_min:row_max, col_min:col_max],
            "lat": data["lat"][row_min:row_max, col_min:col_max],
            "lon": data["lon"][row_min:row_max, col_min:col_max],
            "time": data["time"],
            "filename": data["filename"],
        }

        return bounded_data

    def add_weather_stations(self, ax):
        """添加气象站点标记"""
        for station in self.config["stations"]:
            # 绘制站点标记
            ax.scatter(
                station["lon"],
                station["lat"],
                c=self.config["station_color"],
                s=self.config["station_size"],
                marker="^",
                edgecolors="white",
                linewidth=2,
                zorder=1000,
                alpha=1.0,
            )

            # 添加站点标签
            ax.annotate(
                station["name"],
                (station["lon"], station["lat"]),
                xytext=(5, 5),
                textcoords="offset points",
                fontsize=self.config.get("station_fontsize", 12),
                color=self.config["station_color"],
                fontweight="bold",
                bbox=dict(
                    boxstyle="round,pad=0.3",
                    facecolor="white",
                    alpha=0.8,
                    edgecolor=self.config["station_color"],
                ),
                zorder=1001,
            )

    def plot_temp_wind_subplot(self, ax, data, title, temp_range=None):
        """
        在子图上绘制温度场和风矢量

        Args:
            ax: matplotlib轴对象
            data: 数据字典
            title: 子图标题
            temp_range: 温度范围(min, max)，用于统一色标
        """
        # 应用边界限制
        if self.config["apply_bounds"]:
            data = self.apply_plot_bounds(data)

        lon = data["lon"]
        lat = data["lat"]
        temp = data["temperature"]
        u10 = data["u10"]
        v10 = data["v10"]
        wspd = data["wind_speed"]

        # 设置绘图范围
        if self.config["apply_bounds"]:
            bounds = self.config["plot_bounds"]
            ax.set_xlim(bounds["min_lon"], bounds["max_lon"])
            ax.set_ylim(bounds["min_lat"], bounds["max_lat"])
        else:
            ax.set_xlim(lon.min(), lon.max())
            ax.set_ylim(lat.min(), lat.max())

        # 创建温度色标
        if temp_range is not None:
            temp_min, temp_max = temp_range
        else:
            temp_min, temp_max = temp.min(), temp.max()
        temp_levels = np.linspace(temp_min, temp_max, self.config["contour_levels"])

        # 绘制温度场填充等值线
        cf = ax.contourf(
            lon,
            lat,
            temp,
            levels=temp_levels,
            cmap=self.config["temp_cmap"],
            extend="both",
            alpha=0.9,
        )

        # 添加温度等值线
        if self.config["enable_contour_lines"]:
            cs = ax.contour(
                lon,
                lat,
                temp,
                levels=temp_levels[::2],  # 每隔一个level绘制
                colors="black",
                linewidths=0.5,
                alpha=0.4,
            )
            ax.clabel(
                cs,
                inline=True,
                fontsize=self.config.get("contour_fontsize", 10),
                fmt="%.1f°C",
            )

        # 绘制风矢量箭头
        skip = self.config["wind_arrow_step"]

        # 绘制风矢量
        Q = ax.quiver(
            lon[::skip, ::skip],
            lat[::skip, ::skip],
            u10[::skip, ::skip],
            v10[::skip, ::skip],
            scale=self.config["wind_arrow_scale"],
            scale_units="inches",
            width=self.config["arrow_width"],
            headwidth=self.config["arrow_headwidth"],
            headlength=self.config["arrow_headlength"],
            color=self.config["arrow_color"],  # 使用单一颜色
            alpha=self.config["arrow_alpha"],
            edgecolor="white",  # 白色边缘增强对比
            linewidth=0.5,
        )

        # 添加风矢量参考箭头
        """qk = ax.quiverkey(
            Q,
            0.92,
            0.95,
            3,
            "3 m/s",
            labelpos="E",
            coordinates="axes",
            fontproperties={"size": 9},
            color="black",
        )"""

        # 添加气象站点
        self.add_weather_stations(ax)

        # 添加颜色条
        cbar = plt.colorbar(
            cf, ax=ax, orientation="horizontal", pad=0.08, shrink=0.9, aspect=20
        )
        cbar.set_label("温度 (°C)", fontsize=self.config.get("colorbar_fontsize", 12))
        cbar.ax.tick_params(labelsize=self.config.get("tick_fontsize", 12))

        # 设置坐标轴标签
        ax.set_xlabel("经度 (°)", fontsize=self.config.get("label_fontsize", 14))
        ax.set_ylabel("纬度 (°)", fontsize=self.config.get("label_fontsize", 14))
        ax.set_title(
            title,
            fontsize=self.config.get("title_fontsize", 16),
            fontweight="bold",
            pad=10,
        )

        # 设置刻度数量为4个
        ax.xaxis.set_major_locator(plt.MaxNLocator(4))
        ax.yaxis.set_major_locator(plt.MaxNLocator(4))

        # 设置刻度字体大小
        ax.tick_params(axis="both", labelsize=self.config.get("tick_fontsize", 12))

        # 添加网格
        if self.config["enable_grid"]:
            ax.grid(True, linestyle="--", alpha=0.3, linewidth=0.5)

        # 设置等比例
        ax.set_aspect("equal", adjustable="box")

        # 添加统计信息文本
        stats_text = (
            f"温度: {temp_min:.1f}°C ~ {temp_max:.1f}°C\n"
            f"平均: {temp.mean():.1f}°C\n"
            f"风速: {wspd.min():.1f} ~ {wspd.max():.1f} m/s"
        )
        ax.text(
            0.02,
            0.98,
            stats_text,
            transform=ax.transAxes,
            fontsize=self.config.get("stats_fontsize", 11),
            verticalalignment="top",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
        )

    def plot_comparison(self, wrf1_data, wrf2_data, save_path=None):
        """
        绘制两个WRF文件的温度和风场对比图

        Args:
            wrf1_data: 第一个WRF文件的数据
            wrf2_data: 第二个WRF文件的数据
            save_path: 保存路径（可选）
        """
        # 先计算两个数据集的统一温度范围
        # 应用边界限制后再计算范围
        data1_bounded = (
            self.apply_plot_bounds(wrf1_data)
            if self.config["apply_bounds"]
            else wrf1_data
        )
        data2_bounded = (
            self.apply_plot_bounds(wrf2_data)
            if self.config["apply_bounds"]
            else wrf2_data
        )

        temp_min = min(
            data1_bounded["temperature"].min(), data2_bounded["temperature"].min()
        )
        temp_max = max(
            data1_bounded["temperature"].max(), data2_bounded["temperature"].max()
        )
        unified_temp_range = (temp_min, temp_max)

        print(f"统一温度范围: {temp_min:.1f}°C ~ {temp_max:.1f}°C")

        # 创建图形和子图
        fig, (ax1, ax2) = plt.subplots(
            1, 2, figsize=self.config["figure_size"], dpi=self.config["dpi"]
        )

        # 绘制第一个子图
        title1 = f"YSU_SMAG"
        self.plot_temp_wind_subplot(
            ax1, wrf1_data, title1, temp_range=unified_temp_range
        )

        # 绘制第二个子图
        title2 = f"YSU_SMAG_NBA"
        self.plot_temp_wind_subplot(
            ax2, wrf2_data, title2, temp_range=unified_temp_range
        )

        # 添加总标题
        """fig.suptitle(
            "WRF 2米温度场和10米风场对比分析",
            fontsize=self.config.get("title_fontsize", 16) + 2,  # 总标题稍大
            fontweight="bold",
            y=1.02
        )"""

        # 调整布局
        plt.tight_layout()

        # 保存或显示图形
        if save_path:
            plt.savefig(
                save_path, dpi=self.config["dpi"], bbox_inches="tight", pad_inches=0.1
            )
            print(f"图片已保存到: {save_path}")
        else:
            plt.show()

        plt.close()


def main():
    """主函数"""
    # 配置WRF文件路径
    wrf1_file = r"E:\wrf_out5\wrfout_d04_2022-07-01_14_00_00"
    wrf2_file = r"E:\wrf_out7\wrfout_d04_2022-07-01_14_00_00"

    # 时间索引
    time_idx = 0

    # 创建可视化器
    visualizer = WRFTempWindVisualizer()

    print("=" * 60)
    print("WRF温度和风场可视化分析")
    print("=" * 60)

    # 读取两个WRF文件的数据
    print("\n1. 读取WRF数据...")
    wrf1_data = visualizer.read_wrf_data(wrf1_file, time_idx)
    if not wrf1_data:
        print("读取WRF文件1失败")
        return

    wrf2_data = visualizer.read_wrf_data(wrf2_file, time_idx)
    if not wrf2_data:
        print("读取WRF文件2失败")
        return

    print("\n2. 绘制温度和风场对比图...")
    # 绘制对比图
    visualizer.plot_comparison(
        wrf1_data, wrf2_data, save_path="wrf_temp_wind_comparison.png"
    )

    print("\n分析完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
