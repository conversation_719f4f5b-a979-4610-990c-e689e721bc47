import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr

def analyze_wind_data_chunked(file_path, chunk_size=5000):
    """分块处理大型CSV文件以避免内存问题"""
    print("Reading data in chunks...")
    
    # 初始化存储数组
    all_simulated = []
    all_observed = []
    
    # 分块读取数据
    chunk_iter = pd.read_csv(file_path, chunksize=chunk_size)
    
    for i, chunk in enumerate(chunk_iter):
        print(f"Processing chunk {i+1}...")
        # 提取需要的数据列
        # 模拟结果：75M_75m_wind_speed_ms (第14列，索引为13)
        # 实际观测结果：WS (m/s) (第17列，索引为16)
        simulated_wind_speed = chunk.iloc[:, 13]  # 75M_75m_wind_speed_ms
        observed_wind_speed = chunk.iloc[:, 16]   # WS (m/s)
        
        # 添加到总数组中
        all_simulated.extend(simulated_wind_speed)
        all_observed.extend(observed_wind_speed)
    
    # 转换为numpy数组
    simulated_wind_speed = np.array(all_simulated)
    observed_wind_speed = np.array(all_observed)
    
    # 移除任何NaN值
    mask = ~(np.isnan(simulated_wind_speed) | np.isnan(observed_wind_speed))
    simulated_wind_speed = simulated_wind_speed[mask]
    observed_wind_speed = observed_wind_speed[mask]
    
    print(f"Total valid data points: {len(simulated_wind_speed)}")
    
    # 为了绘图，我们只使用前1000个数据点
    plot_limit = min(1000, len(simulated_wind_speed))
    plot_simulated = simulated_wind_speed[:plot_limit]
    plot_observed = observed_wind_speed[:plot_limit]
    time_points = range(len(plot_simulated))
    
    # 绘制折线图
    print("Generating plot...")
    plt.figure(figsize=(12, 6))
    plt.plot(time_points, plot_simulated, label='Simulated Wind Speed (75M_75m)', linewidth=1)
    plt.plot(time_points, plot_observed, label='Observed Wind Speed (WS)', linewidth=1)
    plt.xlabel('Time Points (5-min intervals)')
    plt.ylabel('Wind Speed (m/s)')
    plt.title('Comparison of Simulated vs Observed Wind Speed')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('wind_speed_comparison_full.png', dpi=300, bbox_inches='tight')
    # plt.show()  # 避免显示图形界面
    
    # 计算相关性指标（使用全部数据）
    print("Calculating metrics...")
    # 1. 相关性系数 (R)
    correlation, p_value = pearsonr(simulated_wind_speed, observed_wind_speed)
    
    # 2. 均方根误差 (RMSE)
    rmse = np.sqrt(mean_squared_error(observed_wind_speed, simulated_wind_speed))
    
    # 3. 平均绝对误差 (MAE)
    mae = mean_absolute_error(observed_wind_speed, simulated_wind_speed)
    
    # 4. 平均偏差 (MB)
    mb = np.mean(simulated_wind_speed - observed_wind_speed)
    
    # 输出结果
    print(f"Correlation Coefficient (R): {correlation:.4f}")
    print(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s")
    print(f"Mean Absolute Error (MAE): {mae:.4f} m/s")
    print(f"Mean Bias (MB): {mb:.4f} m/s")
    
    # 保存统计结果到文本文件
    with open('wind_speed_statistics_full.txt', 'w') as f:
        f.write("Wind Speed Analysis Results (Full Dataset)\n")
        f.write("=" * 40 + "\n")
        f.write(f"Total Data Points: {len(simulated_wind_speed)}\n")
        f.write(f"Correlation Coefficient (R): {correlation:.4f}\n")
        f.write(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s\n")
        f.write(f"Mean Absolute Error (MAE): {mae:.4f} m/s\n")
        f.write(f"Mean Bias (MB): {mb:.4f} m/s\n")
    
    print("Analysis completed. Results saved to 'wind_speed_statistics_full.txt' and plot saved to 'wind_speed_comparison_full.png'")
    return correlation, rmse, mae, mb

# 主程序
if __name__ == "__main__":
    file_path = r'F:\项目\Code\csv\wrf_station_data_5min_parallel_d04.csv'
    analyze_wind_data_chunked(file_path)