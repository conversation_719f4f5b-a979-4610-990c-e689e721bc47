"""
风向分类范围说明
"""

print("="*80)
print("风向分类范围详细说明")
print("="*80)

def get_direction_ranges(n_classes):
    """获取风向分类的范围"""
    class_width = 360 / n_classes
    half_width = class_width / 2

    ranges = []
    for i in range(n_classes):
        center = i * class_width
        # 调整后的范围（使北向居中）
        start = (center - half_width) % 360
        end = (center + half_width) % 360
        ranges.append((i, center, start, end))

    return ranges

# 16方位名称
directions_16 = [
    "N (北)", "NNE (北东北)", "NE (东北)", "ENE (东东北)",
    "E (东)", "ESE (东东南)", "SE (东南)", "SSE (南东南)",
    "S (南)", "SSW (南西南)", "SW (西南)", "WSW (西西南)",
    "W (西)", "WNW (西西北)", "NW (西北)", "NNW (北西北)"
]

# 8方位名称
directions_8 = [
    "N (北)", "NE (东北)", "E (东)", "SE (东南)",
    "S (南)", "SW (西南)", "W (西)", "NW (西北)"
]

print("\n16方位分类范围（每个方位22.5°）:")
print("-"*60)
print(f"{'方位名称':<15} {'中心角度':<10} {'范围':<30}")
print("-"*60)

ranges_16 = get_direction_ranges(16)
for i, center, start, end in ranges_16:
    name = directions_16[i]
    if start > end:  # 跨越0度的情况
        range_str = f"{start:6.2f}° - {end:6.2f}° (跨0°)"
    else:
        range_str = f"{start:6.2f}° - {end:6.2f}°"
    print(f"{name:<15} {center:6.1f}°     {range_str}")

print("\n8方位分类范围（每个方位45°）:")
print("-"*60)
print(f"{'方位名称':<15} {'中心角度':<10} {'范围':<30}")
print("-"*60)

ranges_8 = get_direction_ranges(8)
for i, center, start, end in ranges_8:
    name = directions_8[i]
    if start > end:  # 跨越0度的情况
        range_str = f"{start:6.2f}° - {end:6.2f}° (跨0°)"
    else:
        range_str = f"{start:6.2f}° - {end:6.2f}°"
    print(f"{name:<15} {center:6.1f}°     {range_str}")

print("\n"+"="*80)
print("关键说明：")
print("-"*60)
print("1. 原始定义（未调整）：")
print("   - 16方位：每个方位占22.5°，如N为0°±11.25°")
print("   - 8方位：每个方位占45°，如N为0°±22.5°")
print()
print("2. 代码中的调整（使北向居中）：")
print("   - 为了正确处理跨越0°的边界，代码中将所有角度偏移半个分类宽度")
print("   - 16方位：偏移11.25°")
print("   - 8方位：偏移22.5°")
print()
print("3. 实际范围（标准气象定义）：")
print("-"*60)

# 标准气象定义的范围
print("\n标准16方位范围：")
standard_16 = [
    ("N",   "348.75° - 11.25°"),
    ("NNE", "11.25° - 33.75°"),
    ("NE",  "33.75° - 56.25°"),
    ("ENE", "56.25° - 78.75°"),
    ("E",   "78.75° - 101.25°"),
    ("ESE", "101.25° - 123.75°"),
    ("SE",  "123.75° - 146.25°"),
    ("SSE", "146.25° - 168.75°"),
    ("S",   "168.75° - 191.25°"),
    ("SSW", "191.25° - 213.75°"),
    ("SW",  "213.75° - 236.25°"),
    ("WSW", "236.25° - 258.75°"),
    ("W",   "258.75° - 281.25°"),
    ("WNW", "281.25° - 303.75°"),
    ("NW",  "303.75° - 326.25°"),
    ("NNW", "326.25° - 348.75°"),
]

for name, range_str in standard_16:
    print(f"  {name:4s}: {range_str}")

print("\n标准8方位范围：")
standard_8 = [
    ("N",  "337.5° - 22.5°"),
    ("NE", "22.5° - 67.5°"),
    ("E",  "67.5° - 112.5°"),
    ("SE", "112.5° - 157.5°"),
    ("S",  "157.5° - 202.5°"),
    ("SW", "202.5° - 247.5°"),
    ("W",  "247.5° - 292.5°"),
    ("NW", "292.5° - 337.5°"),
]

for name, range_str in standard_8:
    print(f"  {name:4s}: {range_str}")

print("\n"+"="*80)
print("边界对比：")
print("-"*60)
print("8方位边界点：  22.5°, 67.5°, 112.5°, 157.5°, 202.5°, 247.5°, 292.5°, 337.5°")
print("16方位边界点： 11.25°, 33.75°, 56.25°, 78.75°, 101.25°, 123.75°, 146.25°,")
print("               168.75°, 191.25°, 213.75°, 236.25°, 258.75°, 281.25°,")
print("               303.75°, 326.25°, 348.75°")
print()
print("注意：8方位的每个边界都是16方位的某个方位的中心！")
print("例如：22.5°是16方位NNE的中心，同时是8方位N/NE的边界")

print("\n"+"="*80)
print("测试几个具体角度的分类：")
print("-"*60)

test_angles = [0, 10, 20, 22.5, 30, 45, 350, 355]

for angle in test_angles:
    # 16方位分类
    for i, (name, range_str) in enumerate(standard_16):
        start, end = range_str.split(' - ')
        start_val = float(start.replace('°', ''))
        end_val = float(end.replace('°', ''))

        if start_val > end_val:  # 跨越0度
            if angle >= start_val or angle < end_val:
                class_16 = name
                break
        else:
            if start_val <= angle < end_val:
                class_16 = name
                break

    # 8方位分类
    for i, (name, range_str) in enumerate(standard_8):
        start, end = range_str.split(' - ')
        start_val = float(start.replace('°', ''))
        end_val = float(end.replace('°', ''))

        if start_val > end_val:  # 跨越0度
            if angle >= start_val or angle < end_val:
                class_8 = name
                break
        else:
            if start_val <= angle < end_val:
                class_8 = name
                break

    print(f"{angle:3d}°: 16方位={class_16:4s}, 8方位={class_8}")