#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WRF多时次集合平均与差值分析工具

功能：
1. 读取多个WRF文件（不同时次）
2. 对每个参数化方案的数据进行时间平均
3. 计算不同方案平均值之间的差值
4. 进行统计分析和可视化

作者: 数据分析版本
日期: 2024-12-20
"""

import os
import glob
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import matplotlib.colors as colors
from netCDF4 import Dataset
from datetime import datetime, timedelta
import warnings
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import logging

warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 设置字体支持中文
plt.rcParams.update(
    {
        "font.family": ["Times New Roman", "SimHei"],
        "mathtext.fontset": "stix",
        "axes.unicode_minus": False,
        "figure.max_open_warning": 0,
    }
)


@dataclass
class AnalysisConfig:
    """分析配置类"""

    # 数据路径配置
    wrf_dirs: Dict[str, str] = None  # 不同方案的数据目录
    file_pattern: str = "wrfout_d04_*"  # 文件匹配模式

    # 时间范围配置
    start_time: str = None  # 开始时间 "YYYY-MM-DD_HH"
    end_time: str = None  # 结束时间 "YYYY-MM-DD_HH"
    time_step: int = 1  # 时间步长（小时）

    # 变量配置
    variables: List[str] = None  # 要分析的变量列表

    # 绘图配置
    figure_size: Tuple[int, int] = (15, 10)
    dpi: int = 100
    save_path: str = "./"

    # 统计区域（可选）
    apply_bounds: bool = False
    bounds: Dict = None  # {"min_lon", "max_lon", "min_lat", "max_lat"}

    def __post_init__(self):
        """初始化默认值"""
        if self.wrf_dirs is None:
            self.wrf_dirs = {"YSU_SMAG": r"E:\wrf_out5", "YSU_SMAG_NBA": r"E:\wrf_out7"}

        if self.variables is None:
            self.variables = ["U10", "V10", "T2", "WSPD10"]  # 默认分析变量

        if self.bounds is None:
            self.bounds = {
                "min_lon": 103.934,
                "max_lon": 104.12,
                "min_lat": 32.92,
                "max_lat": 33.07,
            }


class WRFEnsembleAnalyzer:
    """WRF集合数据分析器"""

    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.ensemble_data = {}  # 存储各方案的集合数据
        self.mean_data = {}  # 存储各方案的平均数据
        self.diff_data = {}  # 存储差值数据

    def get_file_list(self, directory: str, pattern: str) -> List[str]:
        """
        获取目录中匹配的文件列表

        Args:
            directory: 数据目录
            pattern: 文件匹配模式

        Returns:
            排序后的文件路径列表
        """
        search_pattern = os.path.join(directory, pattern)
        files = glob.glob(search_pattern)
        files.sort()  # 按文件名排序

        logger.info(f"在 {directory} 中找到 {len(files)} 个文件")
        return files

    def filter_files_by_time(self, files: List[str]) -> List[str]:
        """
        根据时间范围过滤文件

        Args:
            files: 文件列表

        Returns:
            过滤后的文件列表
        """
        if not self.config.start_time or not self.config.end_time:
            return files

        filtered = []
        start_dt = datetime.strptime(self.config.start_time, "%Y-%m-%d_%H")
        end_dt = datetime.strptime(self.config.end_time, "%Y-%m-%d_%H")

        for file in files:
            # 从文件名提取时间
            basename = os.path.basename(file)
            try:
                # WRF文件名格式: wrfout_d04_YYYY-MM-DD_HH_MM_SS
                parts = basename.split("_")
                if len(parts) >= 5:
                    date_str = f"{parts[2]}_{parts[3]}"
                    file_dt = datetime.strptime(date_str, "%Y-%m-%d_%H")

                    if start_dt <= file_dt <= end_dt:
                        filtered.append(file)
            except:
                continue

        logger.info(f"时间过滤后保留 {len(filtered)} 个文件")
        return filtered

    def read_wrf_file(self, filepath: str) -> Dict:
        """
        读取单个WRF文件

        Args:
            filepath: WRF文件路径

        Returns:
            包含变量数据的字典
        """
        try:
            logger.debug(f"读取文件: {filepath}")
            nc = Dataset(filepath, "r")

            data = {}

            # 读取经纬度（只需读取一次）
            if "XLAT" in nc.variables:
                data["lat"] = nc.variables["XLAT"][0, :, :]
                data["lon"] = nc.variables["XLONG"][0, :, :]

            # 读取地形高度数据（HGT）- 只在第一个文件读取
            if "HGT" in nc.variables and "terrain" not in data:
                data["terrain"] = nc.variables["HGT"][0, :, :]  # 地形高度(m)

            # 读取配置的变量
            for var in self.config.variables:
                if var == "WSPD10":  # 计算10米风速
                    if "U10" in nc.variables and "V10" in nc.variables:
                        u10 = nc.variables["U10"][0, :, :]
                        v10 = nc.variables["V10"][0, :, :]
                        data["WSPD10"] = np.sqrt(u10**2 + v10**2)
                elif var in nc.variables:
                    var_data = nc.variables[var][0]
                    if len(var_data.shape) == 2:  # 2D变量
                        data[var] = var_data[:, :]
                    elif len(var_data.shape) == 3:  # 3D变量，取最低层
                        data[var] = var_data[0, :, :]

                    # 单位转换
                    if var == "T2":  # 温度转换为摄氏度
                        data[var] = data[var] - 273.15

            # 获取时间信息
            if "Times" in nc.variables:
                times = nc.variables["Times"][0]
                time_str = "".join(
                    [
                        t.decode("utf-8") if isinstance(t, bytes) else str(t)
                        for t in times
                    ]
                )
                data["time"] = time_str

            nc.close()
            return data

        except Exception as e:
            logger.error(f"读取文件 {filepath} 出错: {e}")
            return None

    def load_ensemble_data(self):
        """
        加载所有方案的集合数据
        """
        logger.info("开始加载集合数据...")

        for scheme_name, directory in self.config.wrf_dirs.items():
            logger.info(f"处理方案: {scheme_name}")

            # 获取文件列表
            files = self.get_file_list(directory, self.config.file_pattern)
            files = self.filter_files_by_time(files)

            if not files:
                logger.warning(f"方案 {scheme_name} 没有找到匹配的文件")
                continue

            # 读取所有时次的数据
            scheme_data = []
            for file in files:
                file_data = self.read_wrf_file(file)
                if file_data:
                    scheme_data.append(file_data)

            if scheme_data:
                self.ensemble_data[scheme_name] = scheme_data
                logger.info(
                    f"方案 {scheme_name} 加载了 {len(scheme_data)} 个时次的数据"
                )

    def calculate_ensemble_mean(self):
        """
        计算每个方案的集合平均
        """
        logger.info("计算集合平均...")

        for scheme_name, data_list in self.ensemble_data.items():
            if not data_list:
                continue

            # 初始化平均数据字典
            mean_data = {}

            # 复制经纬度（所有时次相同）
            mean_data["lat"] = data_list[0]["lat"]
            mean_data["lon"] = data_list[0]["lon"]

            # 复制地形高度数据（所有时次相同）
            if "terrain" in data_list[0]:
                mean_data["terrain"] = data_list[0]["terrain"]

            # 计算每个变量的时间平均
            for var in self.config.variables:
                if var in data_list[0]:
                    # 收集所有时次的数据
                    var_stack = np.stack([d[var] for d in data_list], axis=0)
                    # 计算时间平均
                    mean_data[var] = np.mean(var_stack, axis=0)
                    mean_data[f"{var}_std"] = np.std(
                        var_stack, axis=0
                    )  # 同时计算标准差
                    mean_data[f"{var}_max"] = np.max(var_stack, axis=0)
                    mean_data[f"{var}_min"] = np.min(var_stack, axis=0)

            # 记录时间信息
            mean_data["num_times"] = len(data_list)
            mean_data["times"] = [d["time"] for d in data_list if "time" in d]

            self.mean_data[scheme_name] = mean_data
            logger.info(f"方案 {scheme_name} 的平均值计算完成")

    def calculate_difference(self, scheme1: str, scheme2: str):
        """
        计算两个方案平均值之间的差值

        Args:
            scheme1: 第一个方案名称
            scheme2: 第二个方案名称
        """
        if scheme1 not in self.mean_data or scheme2 not in self.mean_data:
            logger.error(f"方案 {scheme1} 或 {scheme2} 的数据不存在")
            return

        logger.info(f"计算差值: {scheme1} - {scheme2}")

        data1 = self.mean_data[scheme1]
        data2 = self.mean_data[scheme2]

        diff = {
            "lat": data1["lat"],
            "lon": data1["lon"],
            "scheme1": scheme1,
            "scheme2": scheme2,
        }

        # 计算每个变量的差值
        for var in self.config.variables:
            if var in data1 and var in data2:
                diff[f"{var}_diff"] = data1[var] - data2[var]

                # 计算差值统计
                diff_values = diff[f"{var}_diff"]
                diff[f"{var}_diff_stats"] = {
                    "mean": np.mean(diff_values),
                    "std": np.std(diff_values),
                    "max": np.max(diff_values),
                    "min": np.min(diff_values),
                    "abs_max": np.max(np.abs(diff_values)),
                    "positive_ratio": np.sum(diff_values > 0) / diff_values.size * 100,
                    "negative_ratio": np.sum(diff_values < 0) / diff_values.size * 100,
                }

        self.diff_data = diff

    def apply_spatial_bounds(
        self, data: np.ndarray, lon: np.ndarray, lat: np.ndarray
    ) -> Tuple:
        """
        应用空间边界限制

        Args:
            data: 要裁剪的数据
            lon: 经度数组
            lat: 纬度数组

        Returns:
            裁剪后的数据、经度、纬度
        """
        if not self.config.apply_bounds:
            return data, lon, lat

        bounds = self.config.bounds
        mask = (
            (lon >= bounds["min_lon"])
            & (lon <= bounds["max_lon"])
            & (lat >= bounds["min_lat"])
            & (lat <= bounds["max_lat"])
        )

        valid_rows, valid_cols = np.where(mask)
        if len(valid_rows) == 0:
            return data, lon, lat

        row_min, row_max = valid_rows.min(), valid_rows.max() + 1
        col_min, col_max = valid_cols.min(), valid_cols.max() + 1

        return (
            data[row_min:row_max, col_min:col_max],
            lon[row_min:row_max, col_min:col_max],
            lat[row_min:row_max, col_min:col_max],
        )

    def plot_mean_comparison(self, variable: str = "WSPD10"):
        """
        绘制不同方案的平均值对比图（包含差值子图）

        Args:
            variable: 要绘制的变量
        """
        n_schemes = len(self.mean_data)
        if n_schemes == 0:
            logger.error("没有数据可绘制")
            return

        # 如果有两个方案，创建3个子图（两个方案+一个差值）
        if n_schemes == 2 and self.diff_data:
            fig, axes = plt.subplots(1, 3, figsize=(18, 6), dpi=self.config.dpi)
        else:
            fig, axes = plt.subplots(
                1, n_schemes, figsize=self.config.figure_size, dpi=self.config.dpi
            )

        if n_schemes == 1:
            axes = [axes]

        # 找出所有方案的最大最小值，统一色标
        vmin = float("inf")
        vmax = float("-inf")
        for scheme_data in self.mean_data.values():
            if variable in scheme_data:
                data = scheme_data[variable]
                vmin = min(vmin, np.nanmin(data))
                vmax = max(vmax, np.nanmax(data))

        # 创建统一的色标
        levels = np.linspace(vmin, vmax, 20)
        cmap = plt.cm.get_cmap("RdYlBu_r")

        # 绘制各方案的平均值
        for idx, (scheme_name, data) in enumerate(self.mean_data.items()):
            ax = axes[idx]

            if variable not in data:
                ax.set_title(f"{scheme_name}\n(无数据)")
                continue

            # 应用空间边界
            var_data, lon, lat = self.apply_spatial_bounds(
                data[variable], data["lon"], data["lat"]
            )

            # 绘制填充等值线
            cf = ax.contourf(
                lon, lat, var_data, levels=levels, cmap=cmap, extend="both"
            )

            # 添加等值线
            cs = ax.contour(
                lon,
                lat,
                var_data,
                levels=levels[::2],
                colors="black",
                linewidths=0.5,
                alpha=0.5,
            )
            ax.clabel(cs, inline=True, fontsize=8, fmt="%.1f")

            # 设置标题和标签
            mean_val = np.mean(var_data)
            std_val = np.std(var_data)
            ax.set_title(
                f"{scheme_name}",
                fontsize=12,
                fontweight="bold",
            )
            ax.set_xlabel("经度 (°)", fontsize=10)
            ax.set_ylabel("纬度 (°)", fontsize=10)
            ax.set_aspect("equal")

            # 添加颜色条
            plt.colorbar(
                cf,
                ax=ax,
                orientation="horizontal",
                pad=0.1,
                shrink=0.8,
                label=f"{self.get_variable_name(variable)}",
            )

        # 如果有差值数据且有两个方案，绘制差值子图
        if n_schemes == 2 and self.diff_data and f"{variable}_diff" in self.diff_data:
            ax = axes[2]

            # 获取差值数据
            diff_data, lon, lat = self.apply_spatial_bounds(
                self.diff_data[f"{variable}_diff"],
                self.diff_data["lon"],
                self.diff_data["lat"],
            )

            # 创建差值色标
            vmax_diff = np.abs(diff_data).max()
            diff_levels = np.linspace(-vmax_diff, vmax_diff, 21)
            diff_cmap = plt.cm.get_cmap("RdBu_r")

            # 绘制差值填充等值线
            cf_diff = ax.contourf(
                lon, lat, diff_data, levels=diff_levels, cmap=diff_cmap, extend="both"
            )

            # 添加等值线
            cs_diff = ax.contour(
                lon,
                lat,
                diff_data,
                levels=diff_levels[::2],
                colors="black",
                linewidths=0.5,
                alpha=0.5,
            )
            ax.clabel(cs_diff, inline=True, fontsize=8, fmt="%.2f")

            # 添加零线
            # ax.contour(lon, lat, diff_data, levels=[0], colors="green", linewidths=2)

            # 设置标题
            scheme1 = list(self.mean_data.keys())[0]
            scheme2 = list(self.mean_data.keys())[1]
            diff_mean = np.mean(diff_data)
            diff_std = np.std(diff_data)

            ax.set_title(
                f"平均风速差值 ({scheme1} - {scheme2})",
                fontsize=12,
                fontweight="bold",
            )
            ax.set_xlabel("经度 (°)", fontsize=10)
            ax.set_ylabel("纬度 (°)", fontsize=10)
            ax.set_aspect("equal")

            # 添加颜色条
            cbar_diff = plt.colorbar(
                cf_diff, ax=ax, orientation="horizontal", pad=0.1, shrink=0.8
            )
            cbar_diff.set_label(f"风速差值", fontsize=10)

            # 在差值图上添加统计信息
            stats = self.diff_data[f"{variable}_diff_stats"]
            stats_text = (
                f"最大: {stats['max']:.3f}\n"
                f"最小: {stats['min']:.3f}"
                # f"正值: {stats['positive_ratio']:.1f}%"
            )
            ax.text(
                0.02,
                0.98,
                stats_text,
                transform=ax.transAxes,
                bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
                fontsize=12,
                verticalalignment="top",
                horizontalalignment="left",
            )

        # 设置总标题
        var_name = self.get_variable_name(variable)
        # plt.suptitle(f"多时次平均 {var_name} 对比", fontsize=14, fontweight="bold")
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(
            self.config.save_path, f"mean_{variable}_comparison.png"
        )
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches="tight")
        logger.info(f"图形已保存: {save_path}")
        plt.close()

    def plot_difference(self, variable: str = "WSPD10"):
        """
        绘制差值分布图

        Args:
            variable: 要绘制的变量
        """
        if not self.diff_data:
            logger.error("没有差值数据可绘制")
            return

        var_diff = f"{variable}_diff"
        if var_diff not in self.diff_data:
            logger.error(f"变量 {variable} 的差值数据不存在")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6), dpi=self.config.dpi)

        # 应用空间边界
        diff_data, lon, lat = self.apply_spatial_bounds(
            self.diff_data[var_diff], self.diff_data["lon"], self.diff_data["lat"]
        )

        # 1. 差值分布图
        vmax = np.abs(diff_data).max()
        levels = np.linspace(-vmax, vmax, 21)
        cmap = plt.cm.get_cmap("RdBu_r")

        cf = ax1.contourf(lon, lat, diff_data, levels=levels, cmap=cmap, extend="both")
        cs = ax1.contour(
            lon,
            lat,
            diff_data,
            levels=levels[::2],
            colors="black",
            linewidths=0.5,
            alpha=0.5,
        )
        ax1.clabel(cs, inline=True, fontsize=8, fmt="%.1f")

        # 添加零线
        # ax1.contour(lon, lat, diff_data, levels=[0], colors="green", linewidths=2)

        ax1.set_title(
            f"{self.diff_data['scheme1']} - {self.diff_data['scheme2']}",
            fontsize=12,
            fontweight="bold",
        )
        ax1.set_xlabel("经度 (°)", fontsize=10)
        ax1.set_ylabel("纬度 (°)", fontsize=10)
        ax1.set_aspect("equal")

        plt.colorbar(
            cf,
            ax=ax1,
            orientation="horizontal",
            pad=0.1,
            shrink=0.8,
            label=f"{self.get_variable_name(variable)} 差值",
        )

        # 2. 差值直方图
        ax2.hist(
            diff_data.flatten(), bins=50, edgecolor="black", alpha=0.7, color="skyblue"
        )
        ax2.axvline(x=0, color="red", linestyle="--", linewidth=2, label="零线")
        ax2.axvline(
            x=np.mean(diff_data),
            color="green",
            linestyle="-",
            linewidth=2,
            label=f"平均值: {np.mean(diff_data):.2f}",
        )

        # 添加统计信息
        stats = self.diff_data[f"{variable}_diff_stats"]
        stats_text = (
            f"平均差值: {stats['mean']:.3f}\n"
            f"标准差: {stats['std']:.3f}\n"
            f"最大正差值: {stats['max']:.3f}\n"
            f"最大负差值: {stats['min']:.3f}\n"
            f"正差值占比: {stats['positive_ratio']:.1f}%\n"
            f"负差值占比: {stats['negative_ratio']:.1f}%"
        )
        ax2.text(
            0.98,
            0.98,
            stats_text,
            transform=ax2.transAxes,
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
            fontsize=9,
            verticalalignment="top",
            horizontalalignment="right",
        )

        ax2.set_xlabel(f"{self.get_variable_name(variable)} 差值", fontsize=10)
        ax2.set_ylabel("频次", fontsize=10)
        ax2.set_title("差值分布直方图", fontsize=12, fontweight="bold")
        ax2.legend(loc="upper left")
        ax2.grid(True, alpha=0.3)

        # 设置总标题
        var_name = self.get_variable_name(variable)
        plt.suptitle(f"多时次平均 {var_name} 差值分析", fontsize=14, fontweight="bold")
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(self.config.save_path, f"diff_{variable}_analysis.png")
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches="tight")
        logger.info(f"图形已保存: {save_path}")
        plt.close()

    def plot_temporal_std(self, variable: str = "WSPD10"):
        """
        绘制时间标准差分布图

        Args:
            variable: 要绘制的变量
        """
        n_schemes = len(self.mean_data)
        if n_schemes == 0:
            logger.error("没有数据可绘制")
            return

        fig, axes = plt.subplots(
            1, n_schemes, figsize=self.config.figure_size, dpi=self.config.dpi
        )

        if n_schemes == 1:
            axes = [axes]

        for idx, (scheme_name, data) in enumerate(self.mean_data.items()):
            ax = axes[idx]

            var_std = f"{variable}_std"
            if var_std not in data:
                ax.set_title(f"{scheme_name}\n(无标准差数据)")
                continue

            # 应用空间边界
            std_data, lon, lat = self.apply_spatial_bounds(
                data[var_std], data["lon"], data["lat"]
            )

            # 绘制标准差分布
            levels = np.linspace(0, np.max(std_data), 20)
            cf = ax.contourf(
                lon, lat, std_data, levels=levels, cmap="YlOrRd", extend="max"
            )

            ax.set_title(f"{scheme_name}\n时间标准差", fontsize=12, fontweight="bold")
            ax.set_xlabel("经度 (°)", fontsize=10)
            ax.set_ylabel("纬度 (°)", fontsize=10)
            ax.set_aspect("equal")

            plt.colorbar(
                cf,
                ax=ax,
                orientation="horizontal",
                pad=0.1,
                shrink=0.8,
                label=f"{self.get_variable_name(variable)} 标准差",
            )

        plt.suptitle(
            f"{self.get_variable_name(variable)} 时间变异性分析",
            fontsize=14,
            fontweight="bold",
        )
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(self.config.save_path, f"std_{variable}_analysis.png")
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches="tight")
        logger.info(f"图形已保存: {save_path}")
        plt.close()

    def get_variable_name(self, var: str) -> str:
        """获取变量的中文名称"""
        var_names = {
            "U10": "10米U风速",
            "V10": "10米V风速",
            "WSPD10": "10米风速",
            "T2": "2米温度",
            "PSFC": "地面气压",
            "Q2": "2米湿度",
        }
        return var_names.get(var, var)

    def calculate_elevation_statistics(self, variable: str = "WSPD10"):
        """
        计算不同海拔高度段的统计信息

        Args:
            variable: 要分析的变量

        Returns:
            各海拔段的统计信息字典
        """
        if not self.mean_data:
            logger.error("没有平均数据可用于海拔分析")
            return None

        # 获取第一个方案的地形数据（所有方案地形相同）
        first_scheme = list(self.mean_data.keys())[0]
        if "terrain" not in self.mean_data[first_scheme]:
            logger.error("没有地形高度数据")
            return None

        terrain = self.mean_data[first_scheme]["terrain"]

        # 应用空间边界
        if self.config.apply_bounds:
            terrain, lon, lat = self.apply_spatial_bounds(
                terrain,
                self.mean_data[first_scheme]["lon"],
                self.mean_data[first_scheme]["lat"],
            )

        # 定义海拔区间
        min_elev = np.min(terrain)
        max_elev = np.max(terrain)

        # 使用5个固定的海拔分类（2000-4500m）
        elev_bins = np.array([2000, 2500, 3000, 3500, 4000, 4500])
        elev_labels = [
            "2000-2500m",
            "2500-3000m",
            "3000-3500m",
            "3500-4000m",
            "4000-4500m",
        ]

        elevation_stats = {"bins": [], "scheme_stats": {}, "diff_stats": []}

        # 对每个海拔段进行统计
        for i in range(len(elev_bins) - 1):
            lower = elev_bins[i]
            upper = elev_bins[i + 1]
            mask = (terrain >= lower) & (terrain < upper)

            if np.sum(mask) == 0:
                continue

            bin_info = {
                "range": elev_labels[i],
                "lower": lower,
                "upper": upper,
                "count": np.sum(mask),
                "mean_elevation": np.mean(terrain[mask]),
            }

            # 计算每个方案在该海拔段的统计
            for scheme_name, data in self.mean_data.items():
                if variable not in data:
                    continue

                # 应用空间边界和海拔掩码
                var_data, _, _ = self.apply_spatial_bounds(
                    data[variable], data["lon"], data["lat"]
                )

                values_in_bin = var_data[mask]

                if scheme_name not in elevation_stats["scheme_stats"]:
                    elevation_stats["scheme_stats"][scheme_name] = []

                elevation_stats["scheme_stats"][scheme_name].append(
                    {
                        "range": bin_info["range"],
                        "mean": np.mean(values_in_bin),
                        "std": np.std(values_in_bin),
                        "max": np.max(values_in_bin),
                        "min": np.min(values_in_bin),
                    }
                )

            # 如果有差值数据，也进行统计
            if self.diff_data and f"{variable}_diff" in self.diff_data:
                diff_data, _, _ = self.apply_spatial_bounds(
                    self.diff_data[f"{variable}_diff"],
                    self.diff_data["lon"],
                    self.diff_data["lat"],
                )

                diff_in_bin = diff_data[mask]

                bin_info["diff_mean"] = np.mean(diff_in_bin)
                bin_info["diff_std"] = np.std(diff_in_bin)
                bin_info["diff_max"] = np.max(diff_in_bin)
                bin_info["diff_min"] = np.min(diff_in_bin)
                bin_info["positive_ratio"] = (
                    np.sum(diff_in_bin > 0) / len(diff_in_bin) * 100
                )
                bin_info["negative_ratio"] = (
                    np.sum(diff_in_bin < 0) / len(diff_in_bin) * 100
                )

            elevation_stats["bins"].append(bin_info)

        elevation_stats["summary"] = {
            "min_elevation": min_elev,
            "max_elevation": max_elev,
            "num_bins": len(elevation_stats["bins"]),
        }

        return elevation_stats

    def calculate_terrain_slope(self, terrain, lon, lat):
        """
        计算地形坡度和坡向

        Args:
            terrain: 地形高度数组
            lon: 经度数组
            lat: 纬度数组

        Returns:
            slope: 坡度(度)
            aspect: 坡向(度)
        """
        # 计算格点间距（米）
        # 使用平均纬度估算
        mean_lat = np.mean(lat)

        # 经纬度间距
        dlon = np.abs(lon[0, 1] - lon[0, 0])
        dlat = np.abs(lat[1, 0] - lat[0, 0])

        # 转换为米（简化计算）
        dx = dlon * 111000 * np.cos(np.radians(mean_lat))  # 经度方向距离
        dy = dlat * 111000  # 纬度方向距离

        # 计算地形梯度
        dh_dy, dh_dx = np.gradient(terrain, dy, dx)

        # 计算坡度（度）
        slope = np.degrees(np.arctan(np.sqrt(dh_dx**2 + dh_dy**2)))

        # 计算坡向（度，0-360，北为0）
        aspect = np.degrees(np.arctan2(dh_dx, dh_dy))
        aspect[aspect < 0] += 360

        return slope, aspect

    def calculate_slope_statistics(self, variable: str = "WSPD10"):
        """
        计算不同坡度段的统计信息

        Args:
            variable: 要分析的变量

        Returns:
            各坡度段的统计信息字典
        """
        if not self.mean_data:
            logger.error("没有平均数据可用于坡度分析")
            return None

        # 获取第一个方案的地形数据
        first_scheme = list(self.mean_data.keys())[0]
        if "terrain" not in self.mean_data[first_scheme]:
            logger.error("没有地形高度数据")
            return None

        terrain = self.mean_data[first_scheme]["terrain"]
        lon = self.mean_data[first_scheme]["lon"]
        lat = self.mean_data[first_scheme]["lat"]

        # 应用空间边界
        if self.config.apply_bounds:
            terrain, lon, lat = self.apply_spatial_bounds(terrain, lon, lat)

        # 计算坡度
        slope, aspect = self.calculate_terrain_slope(terrain, lon, lat)

        # 定义坡度区间（度） - 减少为5个类别
        slope_bins = np.array([0, 5, 10, 20, 30, 90])  # 坡度分级
        slope_labels = [
            "0-5°",
            "5-10°",
            "10-20°",
            "20-30°",
            ">30°",
        ]

        slope_stats = {
            "bins": [],
            "scheme_stats": {},
            "diff_stats": [],
            "slope_data": slope,
            "aspect_data": aspect,
        }

        # 对每个坡度段进行统计
        for i in range(len(slope_bins) - 1):
            lower = slope_bins[i]
            upper = slope_bins[i + 1]

            if i == len(slope_bins) - 2:  # 最后一个区间
                mask = slope >= lower
            else:
                mask = (slope >= lower) & (slope < upper)

            if np.sum(mask) == 0:
                continue

            bin_info = {
                "range": slope_labels[i],
                "lower": lower,
                "upper": upper,
                "count": np.sum(mask),
                "mean_slope": np.mean(slope[mask]),
            }

            # 计算每个方案在该坡度段的统计
            for scheme_name, data in self.mean_data.items():
                if variable not in data:
                    continue

                # 应用空间边界
                var_data, _, _ = self.apply_spatial_bounds(
                    data[variable], data["lon"], data["lat"]
                )

                values_in_bin = var_data[mask]

                if scheme_name not in slope_stats["scheme_stats"]:
                    slope_stats["scheme_stats"][scheme_name] = []

                slope_stats["scheme_stats"][scheme_name].append(
                    {
                        "range": bin_info["range"],
                        "mean": np.mean(values_in_bin),
                        "std": np.std(values_in_bin),
                        "max": np.max(values_in_bin),
                        "min": np.min(values_in_bin),
                    }
                )

            # 如果有差值数据，也进行统计
            if self.diff_data and f"{variable}_diff" in self.diff_data:
                diff_data, _, _ = self.apply_spatial_bounds(
                    self.diff_data[f"{variable}_diff"],
                    self.diff_data["lon"],
                    self.diff_data["lat"],
                )

                diff_in_bin = diff_data[mask]

                bin_info["diff_mean"] = np.mean(diff_in_bin)
                bin_info["diff_std"] = np.std(diff_in_bin)
                bin_info["diff_max"] = np.max(diff_in_bin)
                bin_info["diff_min"] = np.min(diff_in_bin)
                bin_info["positive_ratio"] = (
                    np.sum(diff_in_bin > 0) / len(diff_in_bin) * 100
                )
                bin_info["negative_ratio"] = (
                    np.sum(diff_in_bin < 0) / len(diff_in_bin) * 100
                )

            slope_stats["bins"].append(bin_info)

        slope_stats["summary"] = {
            "min_slope": np.min(slope),
            "max_slope": np.max(slope),
            "mean_slope": np.mean(slope),
            "num_bins": len(slope_stats["bins"]),
        }

        return slope_stats

    def plot_slope_analysis(self, variable: str = "WSPD10"):
        """
        绘制坡度相关的分析图

        Args:
            variable: 要分析的变量
        """
        # 计算坡度统计
        slope_stats = self.calculate_slope_statistics(variable)

        if not slope_stats or not slope_stats["bins"]:
            logger.error("无法进行坡度分析")
            return

        # 创建图形
        fig = plt.figure(figsize=(16, 10))

        # 1. 坡度分布图
        ax1 = plt.subplot(2, 3, 1)
        slope_data = slope_stats["slope_data"]
        first_scheme = list(self.mean_data.keys())[0]
        lon = self.mean_data[first_scheme]["lon"]
        lat = self.mean_data[first_scheme]["lat"]

        # 应用边界
        if self.config.apply_bounds:
            _, lon, lat = self.apply_spatial_bounds(slope_data, lon, lat)

        # 绘制坡度分布
        levels = [0, 2, 5, 10, 15, 20, 30, 45, 90]
        cf = ax1.contourf(
            lon, lat, slope_data, levels=levels, cmap="YlOrRd", extend="max"
        )
        ax1.set_title("地形坡度分布", fontsize=12, fontweight="bold")
        ax1.set_xlabel("经度 (°)", fontsize=10)
        ax1.set_ylabel("纬度 (°)", fontsize=10)
        ax1.set_aspect("equal")
        cbar = plt.colorbar(cf, ax=ax1, label="坡度 (°)")
        cbar.set_ticks(levels)

        # 2. 不同方案在各坡度段的平均值
        ax2 = plt.subplot(2, 3, 2)
        x_labels = [b["range"] for b in slope_stats["bins"]]
        x_pos = np.arange(len(x_labels))

        bar_width = 0.35
        for idx, (scheme_name, scheme_data) in enumerate(
            slope_stats["scheme_stats"].items()
        ):
            means = [d["mean"] for d in scheme_data]
            ax2.bar(
                x_pos + idx * bar_width, means, bar_width, label=scheme_name, alpha=0.8
            )

        ax2.set_xlabel("坡度等级", fontsize=10)
        ax2.set_ylabel(f"{self.get_variable_name(variable)}", fontsize=10)
        ax2.set_title("不同坡度段的平均值对比", fontsize=12, fontweight="bold")
        ax2.set_xticks(x_pos + bar_width / 2)
        ax2.set_xticklabels(x_labels, rotation=0, ha="center")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 各坡度段的差值
        if self.diff_data:
            ax3 = plt.subplot(2, 3, 3)
            diff_means = [b.get("diff_mean", 0) for b in slope_stats["bins"]]
            diff_stds = [b.get("diff_std", 0) for b in slope_stats["bins"]]

            colors = ["green" if d < 0 else "red" for d in diff_means]
            bars = ax3.bar(
                x_pos, diff_means, yerr=diff_stds, capsize=5, color=colors, alpha=0.6
            )

            ax3.axhline(y=0, color="black", linestyle="-", linewidth=1)
            ax3.set_xlabel("坡度等级", fontsize=10)
            ax3.set_ylabel(f"{self.get_variable_name(variable)} 差值", fontsize=10)
            ax3.set_title("各坡度段的差值分析", fontsize=12, fontweight="bold")
            ax3.set_xticks(x_pos)
            ax3.set_xticklabels(x_labels, rotation=0, ha="center")
            ax3.grid(True, alpha=0.3)

            # 在柱子上添加数值
            for bar, mean_val in zip(bars, diff_means):
                height = bar.get_height()
                ax3.text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height,
                    f"{mean_val:.3f}",
                    ha="center",
                    va="bottom" if height > 0 else "top",
                    fontsize=8,
                )

        # 4. 坡度与差值的散点图
        if self.diff_data:
            ax4 = plt.subplot(2, 3, 4)

            # 获取差值数据
            diff_data, _, _ = self.apply_spatial_bounds(
                self.diff_data[f"{variable}_diff"],
                self.diff_data["lon"],
                self.diff_data["lat"],
            )

            # 采样绘制（避免点太多）
            sample_step = max(1, slope_data.size // 5000)
            slope_flat = slope_data.flatten()[::sample_step]
            diff_flat = diff_data.flatten()[::sample_step]

            # 绘制散点图
            scatter = ax4.scatter(
                slope_flat,
                diff_flat,
                c=diff_flat,
                cmap="RdBu_r",
                alpha=0.3,
                s=1,
                vmin=-np.abs(diff_flat).max(),
                vmax=np.abs(diff_flat).max(),
            )

            # 添加趋势线
            z = np.polyfit(slope_flat, diff_flat, 1)
            p = np.poly1d(z)
            x_trend = np.linspace(slope_flat.min(), slope_flat.max(), 100)
            ax4.plot(
                x_trend,
                p(x_trend),
                "r-",
                linewidth=2,
                label=f"趋势: {z[0]:.4f}x + {z[1]:.4f}",
            )

            ax4.axhline(y=0, color="black", linestyle="--", alpha=0.5)
            ax4.set_xlabel("坡度 (°)", fontsize=10)
            ax4.set_ylabel(f"{self.get_variable_name(variable)} 差值", fontsize=10)
            ax4.set_title("坡度-差值关系", fontsize=12, fontweight="bold")
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=ax4, label="差值")

        # 5. 各坡度段的数据点分布
        ax5 = plt.subplot(2, 3, 5)
        counts = [b["count"] for b in slope_stats["bins"]]

        # 创建饼图
        colors_pie = plt.cm.YlOrRd(np.linspace(0.3, 0.9, len(counts)))
        wedges, texts, autotexts = ax5.pie(
            counts, labels=x_labels, colors=colors_pie, autopct="%1.1f%%", startangle=90
        )

        for autotext in autotexts:
            autotext.set_fontsize(8)
            autotext.set_color("white")
            autotext.set_fontweight("bold")

        ax5.set_title("各坡度段数据点占比", fontsize=12, fontweight="bold")

        # 6. 坡度统计摘要
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis("off")

        summary_text = f"坡度统计摘要\n" + "=" * 30 + "\n\n"
        summary_text += f"坡度范围: {slope_stats['summary']['min_slope']:.1f}° - "
        summary_text += f"{slope_stats['summary']['max_slope']:.1f}°\n"
        summary_text += f"平均坡度: {slope_stats['summary']['mean_slope']:.1f}°\n"
        summary_text += f"分析类别: {slope_stats['summary']['num_bins']}\n\n"

        if self.diff_data:
            summary_text += f"{self.get_variable_name(variable)}差值统计:\n"
            for bin_info in slope_stats["bins"]:
                if "diff_mean" in bin_info:
                    summary_text += f"\n{bin_info['range']}:\n"
                    summary_text += f"  平均差值: {bin_info['diff_mean']:.3f}\n"
                    summary_text += f"  点数: {bin_info['count']}\n"

        ax6.text(
            0.1,
            0.9,
            summary_text,
            transform=ax6.transAxes,
            fontsize=10,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
        )

        # 设置总标题
        plt.suptitle(
            f"{self.get_variable_name(variable)} 地形坡度分层分析",
            fontsize=14,
            fontweight="bold",
        )
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(
            self.config.save_path, f"slope_{variable}_analysis.png"
        )
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches="tight")
        logger.info(f"坡度分析图已保存: {save_path}")
        plt.close()

    def plot_elevation_analysis(self, variable: str = "WSPD10"):
        """
        绘制海拔高度相关的分析图

        Args:
            variable: 要分析的变量
        """
        # 计算海拔统计
        elev_stats = self.calculate_elevation_statistics(variable)

        if not elev_stats or not elev_stats["bins"]:
            logger.error("无法进行海拔分析")
            return

        # 创建图形
        fig = plt.figure(figsize=(16, 10))

        # 1. 地形高度分布图
        ax1 = plt.subplot(2, 3, 1)
        first_scheme = list(self.mean_data.keys())[0]
        terrain = self.mean_data[first_scheme]["terrain"]
        lon = self.mean_data[first_scheme]["lon"]
        lat = self.mean_data[first_scheme]["lat"]

        # 应用边界
        if self.config.apply_bounds:
            terrain, lon, lat = self.apply_spatial_bounds(terrain, lon, lat)

        # 绘制地形
        levels = np.linspace(terrain.min(), terrain.max(), 20)
        cf = ax1.contourf(
            lon, lat, terrain, levels=levels, cmap="terrain", extend="both"
        )
        ax1.set_title("地形高度分布", fontsize=12, fontweight="bold")
        ax1.set_xlabel("经度 (°)", fontsize=10)
        ax1.set_ylabel("纬度 (°)", fontsize=10)
        ax1.set_aspect("equal")
        plt.colorbar(cf, ax=ax1, label="高度 (m)")

        # 2. 不同方案在各海拔段的平均值
        ax2 = plt.subplot(2, 3, 2)
        x_labels = [b["range"] for b in elev_stats["bins"]]
        x_pos = np.arange(len(x_labels))

        bar_width = 0.35
        for idx, (scheme_name, scheme_data) in enumerate(
            elev_stats["scheme_stats"].items()
        ):
            means = [d["mean"] for d in scheme_data]
            ax2.bar(
                x_pos + idx * bar_width, means, bar_width, label=scheme_name, alpha=0.8
            )

        ax2.set_xlabel("海拔高度段", fontsize=10)
        ax2.set_ylabel(f"{self.get_variable_name(variable)}", fontsize=10)
        ax2.set_title("不同海拔段的平均值对比", fontsize=12, fontweight="bold")
        ax2.set_xticks(x_pos + bar_width / 2)
        ax2.set_xticklabels(x_labels, rotation=0, ha="center")
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 各海拔段的差值
        if self.diff_data:
            ax3 = plt.subplot(2, 3, 3)
            diff_means = [b.get("diff_mean", 0) for b in elev_stats["bins"]]
            diff_stds = [b.get("diff_std", 0) for b in elev_stats["bins"]]

            ax3.errorbar(
                x_pos,
                diff_means,
                yerr=diff_stds,
                fmt="o-",
                capsize=5,
                capthick=2,
                label="平均差值±标准差",
            )
            ax3.axhline(y=0, color="red", linestyle="--", alpha=0.5)
            ax3.set_xlabel("海拔高度段", fontsize=10)
            ax3.set_ylabel(f"{self.get_variable_name(variable)} 差值", fontsize=10)
            ax3.set_title("各海拔段的差值分析", fontsize=12, fontweight="bold")
            ax3.set_xticks(x_pos)
            ax3.set_xticklabels(x_labels, rotation=0, ha="center")
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # 4. 正负差值占比随海拔变化
        if self.diff_data:
            ax4 = plt.subplot(2, 3, 4)
            positive_ratios = [b.get("positive_ratio", 50) for b in elev_stats["bins"]]
            negative_ratios = [b.get("negative_ratio", 50) for b in elev_stats["bins"]]

            ax4.bar(
                x_pos,
                positive_ratios,
                bar_width,
                label="正差值占比",
                color="red",
                alpha=0.6,
            )
            ax4.bar(
                x_pos + bar_width,
                negative_ratios,
                bar_width,
                label="负差值占比",
                color="blue",
                alpha=0.6,
            )
            ax4.axhline(y=50, color="black", linestyle="--", alpha=0.5)
            ax4.set_xlabel("海拔高度段", fontsize=10)
            ax4.set_ylabel("占比 (%)", fontsize=10)
            ax4.set_title("正负差值占比随海拔变化", fontsize=12, fontweight="bold")
            ax4.set_xticks(x_pos + bar_width / 2)
            ax4.set_xticklabels(x_labels, rotation=0, ha="center")
            ax4.legend()
            ax4.set_ylim([0, 100])
            ax4.grid(True, alpha=0.3)

        # 5. 各海拔段的数据点数量
        ax5 = plt.subplot(2, 3, 5)
        counts = [b["count"] for b in elev_stats["bins"]]
        bars = ax5.bar(x_pos, counts, color="green", alpha=0.6)
        ax5.set_xlabel("海拔高度段", fontsize=10)
        ax5.set_ylabel("网格点数", fontsize=10)
        ax5.set_title("各海拔段的数据点分布", fontsize=12, fontweight="bold")
        ax5.set_xticks(x_pos)
        ax5.set_xticklabels(x_labels, rotation=0, ha="center")

        # 在柱子上添加数值
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax5.text(
                bar.get_x() + bar.get_width() / 2.0,
                height,
                f"{count}",
                ha="center",
                va="bottom",
                fontsize=8,
            )

        ax5.grid(True, alpha=0.3)

        # 6. 海拔统计摘要
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis("off")

        summary_text = f"海拔统计摘要\n" + "=" * 30 + "\n\n"
        summary_text += f"海拔范围: {elev_stats['summary']['min_elevation']:.0f} - "
        summary_text += f"{elev_stats['summary']['max_elevation']:.0f} m\n"
        summary_text += f"分析层数: {elev_stats['summary']['num_bins']}\n\n"

        if self.diff_data:
            summary_text += "差值统计:\n"
            for i, bin_info in enumerate(elev_stats["bins"][:5]):  # 只显示前5个
                summary_text += f"\n{bin_info['range']}:\n"
                summary_text += f"  平均差值: {bin_info.get('diff_mean', 0):.3f}\n"
                summary_text += (
                    f"  正差值占比: {bin_info.get('positive_ratio', 50):.1f}%\n"
                )

        ax6.text(
            0.1,
            0.9,
            summary_text,
            transform=ax6.transAxes,
            fontsize=10,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
        )

        # 设置总标题
        plt.suptitle(
            f"{self.get_variable_name(variable)} 海拔高度分层分析",
            fontsize=14,
            fontweight="bold",
        )
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(
            self.config.save_path, f"elevation_{variable}_analysis.png"
        )
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches="tight")
        logger.info(f"海拔分析图已保存: {save_path}")
        plt.close()

    def plot_combined_terrain_analysis(self, variable: str = "WSPD10"):
        """
        绘制海拔和坡度的组合风速对比图

        Args:
            variable: 要分析的变量
        """
        # 计算海拔和坡度统计
        elev_stats = self.calculate_elevation_statistics(variable)
        slope_stats = self.calculate_slope_statistics(variable)

        if (
            not elev_stats
            or not elev_stats["bins"]
            or not slope_stats
            or not slope_stats["bins"]
        ):
            logger.error("无法进行地形分析")
            return

        # 创建图形 - 两个子图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # 获取方案名称
        schemes = list(self.mean_data.keys())

        # 1. 海拔高度平均值对比
        x_labels_elev = [b["range"] for b in elev_stats["bins"]]
        x = np.arange(len(x_labels_elev))
        width = 0.35

        # 准备数据
        values1_elev = []
        values2_elev = []

        # 从 scheme_stats 中提取数据
        if schemes[0] in elev_stats["scheme_stats"]:
            for stat in elev_stats["scheme_stats"][schemes[0]]:
                values1_elev.append(stat["mean"])

        if len(schemes) > 1 and schemes[1] in elev_stats["scheme_stats"]:
            for stat in elev_stats["scheme_stats"][schemes[1]]:
                values2_elev.append(stat["mean"])

        # 绘制柱状图
        bars1 = ax1.bar(
            x - width / 2, values1_elev, width, label=schemes[0], color="#1f77b4"
        )
        if len(schemes) > 1 and values2_elev:
            bars2 = ax1.bar(
                x + width / 2, values2_elev, width, label=schemes[1], color="#ff7f0e"
            )

        ax1.set_xlabel("海拔高度", fontsize=12, fontweight="bold")
        ax1.set_ylabel(self.get_variable_name(variable), fontsize=12, fontweight="bold")
        ax1.set_title("不同海拔平均风速", fontsize=14, fontweight="bold")
        ax1.set_xticks(x)
        ax1.set_xticklabels(x_labels_elev, rotation=0, ha="center")
        ax1.legend(loc="upper left", fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 2. 坡度平均值对比
        x_labels_slope = [b["range"] for b in slope_stats["bins"]]
        x = np.arange(len(x_labels_slope))

        # 准备数据
        values1_slope = []
        values2_slope = []

        # 从 scheme_stats 中提取数据
        if schemes[0] in slope_stats["scheme_stats"]:
            for stat in slope_stats["scheme_stats"][schemes[0]]:
                values1_slope.append(stat["mean"])

        if len(schemes) > 1 and schemes[1] in slope_stats["scheme_stats"]:
            for stat in slope_stats["scheme_stats"][schemes[1]]:
                values2_slope.append(stat["mean"])

        # 绘制柱状图
        bars1 = ax2.bar(
            x - width / 2, values1_slope, width, label=schemes[0], color="#1f77b4"
        )
        if len(schemes) > 1 and values2_slope:
            bars2 = ax2.bar(
                x + width / 2, values2_slope, width, label=schemes[1], color="#ff7f0e"
            )

        ax2.set_xlabel("坡度等级", fontsize=12, fontweight="bold")
        ax2.set_ylabel(self.get_variable_name(variable), fontsize=12, fontweight="bold")
        ax2.set_title("不同坡度平均值风速", fontsize=14, fontweight="bold")
        ax2.set_xticks(x)
        ax2.set_xticklabels(x_labels_slope, rotation=0, ha="center")
        ax2.legend(loc="upper right", fontsize=10)
        ax2.grid(True, alpha=0.3)

        # 设置总标题
        """plt.suptitle(
            f"{self.get_variable_name(variable)} 地形分层对比分析",
            fontsize=16,
            fontweight="bold",
            y=1.02,
        )"""
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(
            self.config.save_path, f"combined_terrain_{variable}_analysis.png"
        )
        plt.savefig(save_path, dpi=self.config.dpi, bbox_inches="tight")
        logger.info(f"地形组合分析图已保存: {save_path}")
        plt.close()

    def generate_report(self):
        """生成分析报告"""
        report = []
        report.append("=" * 60)
        report.append("WRF多时次集合平均与差值分析报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # 数据概况
        report.append("1. 数据概况")
        report.append("-" * 40)
        for scheme_name, data in self.mean_data.items():
            report.append(f"方案: {scheme_name}")
            report.append(f"  - 时次数量: {data['num_times']}")
            if data["times"]:
                report.append(f"  - 起始时间: {data['times'][0]}")
                report.append(f"  - 结束时间: {data['times'][-1]}")
            report.append("")

        # 差值统计
        if self.diff_data:
            report.append("2. 差值统计")
            report.append("-" * 40)
            report.append(
                f"对比方案: {self.diff_data['scheme1']} - {self.diff_data['scheme2']}\n"
            )

            for var in self.config.variables:
                var_stats = f"{var}_diff_stats"
                if var_stats in self.diff_data:
                    stats = self.diff_data[var_stats]
                    report.append(f"{self.get_variable_name(var)}:")
                    report.append(f"  - 平均差值: {stats['mean']:.4f}")
                    report.append(f"  - 标准差: {stats['std']:.4f}")
                    report.append(f"  - 最大差值: {stats['max']:.4f}")
                    report.append(f"  - 最小差值: {stats['min']:.4f}")
                    report.append(f"  - 正差值占比: {stats['positive_ratio']:.1f}%")
                    report.append(f"  - 负差值占比: {stats['negative_ratio']:.1f}%")
                    report.append("")

        # 保存报告
        report_path = os.path.join(self.config.save_path, "analysis_report.txt")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write("\n".join(report))

        logger.info(f"分析报告已保存: {report_path}")

        # 同时打印报告
        print("\n".join(report))


def main():
    """主函数"""

    # 创建配置
    config = AnalysisConfig(
        wrf_dirs={"YSU_SMAG": r"E:\wrf_out5", "YSU_SMAG_NBA": r"E:\wrf_out7"},
        file_pattern="wrfout_d04_2022-07-*",
        start_time="2022-07-01_01",
        end_time="2022-07-03_00",
        variables=["U10", "V10", "WSPD10", "T2"],
        figure_size=(16, 8),
        dpi=800,
        apply_bounds=True,
        save_path="./ensemble_analysis",
    )

    # 创建输出目录
    os.makedirs(config.save_path, exist_ok=True)

    # 创建分析器
    analyzer = WRFEnsembleAnalyzer(config)

    print("=" * 60)
    print("WRF多时次集合平均与差值分析")
    print("=" * 60)

    # 1. 加载数据
    print("\n1. 加载集合数据...")
    analyzer.load_ensemble_data()

    # 2. 计算集合平均
    print("\n2. 计算集合平均...")
    analyzer.calculate_ensemble_mean()

    # 3. 计算差值
    print("\n3. 计算方案间差值...")
    schemes = list(analyzer.mean_data.keys())
    if len(schemes) >= 2:
        analyzer.calculate_difference(schemes[0], schemes[1])

    # 4. 生成可视化
    print("\n4. 生成可视化图表...")

    # 绘制风速相关图形
    if "WSPD10" in config.variables:
        print("   - 绘制风速平均值对比...")
        analyzer.plot_mean_comparison("WSPD10")

        print("   - 绘制风速差值分析...")
        analyzer.plot_difference("WSPD10")

        print("   - 绘制风速时间标准差...")
        analyzer.plot_temporal_std("WSPD10")

        print("   - 绘制风速海拔高度分析...")
        analyzer.plot_elevation_analysis("WSPD10")

        print("   - 绘制风速坡度分析...")
        analyzer.plot_slope_analysis("WSPD10")

        print("   - 绘制风速地形组合分析...")
        analyzer.plot_combined_terrain_analysis("WSPD10")

    # 绘制温度相关图形
    if "T2" in config.variables:
        print("   - 绘制温度平均值对比...")
        analyzer.plot_mean_comparison("T2")

        print("   - 绘制温度差值分析...")
        analyzer.plot_difference("T2")

        print("   - 绘制温度海拔高度分析...")
        analyzer.plot_elevation_analysis("T2")

        print("   - 绘制温度坡度分析...")
        analyzer.plot_slope_analysis("T2")

    # 5. 生成报告
    print("\n5. 生成分析报告...")
    analyzer.generate_report()

    print("\n分析完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
