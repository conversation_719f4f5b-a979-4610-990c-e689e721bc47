"""
高性能密度散点图生成工具
优化版本：支持大数据量、并行处理、内存优化
"""

import gc
import warnings
from typing import Tuple, Optional, Union
from multiprocessing import Pool, cpu_count
from pathlib import Path

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from scipy.stats import linregress
from sklearn.metrics import mean_squared_error, mean_absolute_error, explained_variance_score
from matplotlib import rcParams
from mpl_toolkits.axes_grid1 import make_axes_locatable
from tqdm import tqdm

warnings.filterwarnings('ignore')

# ============== 配置参数 ==============
class Config:
    """全局配置类"""
    # 字体设置
    FONT_FAMILY = "Times New Roman"
    FONT_SIZE = 22
    MATHTEXT_FONTSET = "stix"
    
    # 图形元素字体大小
    AXIS_LABEL_SIZE = 30
    AXIS_TICK_SIZE = 30
    TEXT_SIZE = 30
    COLORBAR_LABEL_SIZE = 30
    COLORBAR_TICK_SIZE = 30
    
    # 图形元素样式
    SCATTER_SIZE = 30
    COLORBAR_PAD = 0.15
    COLORBAR_WIDTH = "3%"
    
    # 性能优化参数
    N_CORES = min(cpu_count(), 16)
    MEMORY_LIMIT = False
    MAX_SAMPLE_SIZE = 1000000
    DENSITY_SAMPLE_SIZE = 50000
    USE_FAST_DENSITY = True
    HISTOGRAM_BINS = 100
    
    # 图像质量参数
    FIGURE_SIZE = (10, 10)
    DPI_HIGH = 1200
    DPI_OPTIMIZED = 600
    
    # 文件路径
    INPUT_FILE = "F:/项目/density_plot_1.xlsx"
    OUTPUT_FILE = "density_scatter_plot_20.png"
    
    # 数据列名
    X_COLUMN = "cosi_20240428"
    Y_COLUMN = "B8"
    Y_LABEL = "NIR Reflectance"


# ============== 初始化设置 ==============
def setup_matplotlib():
    """设置matplotlib全局参数"""
    config = {
        "font.family": Config.FONT_FAMILY,
        "font.size": Config.FONT_SIZE,
        "mathtext.fontset": Config.MATHTEXT_FONTSET
    }
    rcParams.update(config)


# ============== 数据处理函数 ==============
def load_and_preprocess_data(file_path: str, x_col: str, y_col: str, 
                             max_samples: Optional[int] = None) -> Tuple[pd.Series, pd.Series]:
    """
    加载并预处理数据
    
    Args:
        file_path: Excel文件路径
        x_col: X轴列名
        y_col: Y轴列名
        max_samples: 最大样本数量限制
    
    Returns:
        预处理后的x和y数据序列
    """
    # 加载数据
    data = pd.read_excel(file_path)
    
    # 数据过滤：限制在[0, 1]范围内
    mask = (data[x_col] < 1) & (data[y_col] < 1)
    data_filtered = data[mask]
    
    # 智能采样
    if max_samples and len(data_filtered) > max_samples:
        print(f"数据量过大 ({len(data_filtered):,} 条)，采样至 {max_samples:,} 条")
        data_filtered = data_filtered.sample(max_samples, random_state=42)
    else:
        print(f"处理数据量：{len(data_filtered):,} 条")
    
    return data_filtered[x_col], data_filtered[y_col]


# ============== 统计指标计算 ==============
class MetricsCalculator:
    """评估指标计算器"""
    
    @staticmethod
    def calculate_all_metrics(x: np.ndarray, y: np.ndarray, use_parallel: bool = True) -> dict:
        """
        计算所有评估指标
        
        Args:
            x: 预测值
            y: 真实值
            use_parallel: 是否使用并行计算
        
        Returns:
            包含所有指标的字典
        """
        if use_parallel and len(x) > 10000:
            return MetricsCalculator._parallel_metrics(x, y)
        else:
            return MetricsCalculator._sequential_metrics(x, y)
    
    @staticmethod
    def _sequential_metrics(x: np.ndarray, y: np.ndarray) -> dict:
        """顺序计算指标"""
        return {
            'bias': np.mean(x - y),
            'mse': mean_squared_error(x, y),
            'rmse': np.sqrt(mean_squared_error(x, y)),
            'mae': mean_absolute_error(x, y),
            'ev': explained_variance_score(x, y)
        }
    
    @staticmethod
    def _parallel_metrics(x: np.ndarray, y: np.ndarray) -> dict:
        """并行计算指标"""
        with Pool(min(Config.N_CORES, 4)) as pool:
            tasks = [
                ('bias', pool.apply_async(np.mean, (x - y,))),
                ('mse', pool.apply_async(mean_squared_error, (x, y))),
                ('mae', pool.apply_async(mean_absolute_error, (x, y))),
                ('ev', pool.apply_async(explained_variance_score, (x, y)))
            ]
            
            results = {name: task.get() for name, task in tasks}
            results['rmse'] = np.sqrt(results['mse'])
            
        return results


# ============== 密度计算函数 ==============
class DensityCalculator:
    """密度计算器"""
    
    @staticmethod
    def calculate_adaptive(x: pd.Series, y: pd.Series) -> np.ndarray:
        """
        自适应密度计算：根据数据量自动选择最佳算法
        
        Args:
            x: X轴数据
            y: Y轴数据
        
        Returns:
            每个点的密度值
        """
        data_size = len(x)
        
        if Config.USE_FAST_DENSITY and data_size > 200000:
            return DensityCalculator.calculate_histogram(x, y, Config.HISTOGRAM_BINS)
        elif Config.MEMORY_LIMIT and data_size > 50000:
            return DensityCalculator.calculate_kde_sampled(x, y, Config.DENSITY_SAMPLE_SIZE)
        else:
            return DensityCalculator.calculate_kde_full(x, y)
    
    @staticmethod
    def calculate_kde_full(x: pd.Series, y: pd.Series) -> np.ndarray:
        """完整KDE计算（高精度）"""
        print("使用传统KDE方法（高精度）")
        xy = np.vstack([x, y])
        kde = stats.gaussian_kde(xy)
        return kde(xy)
    
    @staticmethod
    def calculate_kde_sampled(x: pd.Series, y: pd.Series, sample_size: int) -> np.ndarray:
        """采样KDE计算（平衡速度和精度）"""
        if len(x) > sample_size:
            indices = np.random.choice(len(x), sample_size, replace=False)
            x_sample = x.iloc[indices]
            y_sample = y.iloc[indices]
            print(f"密度计算采样：{len(x):,} -> {sample_size:,} 条数据")
        else:
            x_sample, y_sample = x, y
        
        xy_sample = np.vstack([x_sample, y_sample])
        kde = stats.gaussian_kde(xy_sample, bw_method='scott')
        
        xy_full = np.vstack([x, y])
        return kde(xy_full)
    
    @staticmethod
    def calculate_histogram(x: pd.Series, y: pd.Series, bins: int = 100) -> np.ndarray:
        """直方图密度计算（超快速）"""
        print(f"使用快速密度计算方法（{bins}x{bins} 网格）")
        
        hist, x_edges, y_edges = np.histogram2d(
            x, y, bins=bins, 
            range=[[0, 1], [0, 1]], 
            density=True
        )
        
        x_indices = np.clip(np.digitize(x, x_edges) - 1, 0, bins - 1)
        y_indices = np.clip(np.digitize(y, y_edges) - 1, 0, bins - 1)
        
        return hist[x_indices, y_indices]


# ============== 绘图函数 ==============
class PlotGenerator:
    """图形生成器"""
    
    def __init__(self, x: pd.Series, y: pd.Series, density: np.ndarray):
        """
        初始化绘图生成器
        
        Args:
            x: X轴数据
            y: Y轴数据
            density: 密度值
        """
        self.x = x
        self.y = y
        self.density = density
        self._sort_by_density()
    
    def _sort_by_density(self):
        """按密度排序数据点"""
        idx = self.density.argsort()
        self.x_sorted = self.x.iloc[idx]
        self.y_sorted = self.y.iloc[idx]
        self.z_sorted = self.density[idx]
    
    def create_figure(self) -> Tuple[plt.Figure, plt.Axes]:
        """创建图形和坐标轴"""
        dpi = Config.DPI_OPTIMIZED if Config.MEMORY_LIMIT else Config.DPI_HIGH
        fig, ax = plt.subplots(figsize=Config.FIGURE_SIZE, dpi=dpi)
        return fig, ax
    
    def plot_scatter(self, ax: plt.Axes) -> plt.scatter:
        """绘制散点图"""
        scatter = ax.scatter(
            self.x_sorted, self.y_sorted,
            c=self.z_sorted * 100,
            s=Config.SCATTER_SIZE,
            cmap="Spectral_r",
            edgecolors="none",
            alpha=0.8
        )
        return scatter
    
    def add_colorbar(self, ax: plt.Axes, scatter: plt.scatter):
        """添加颜色条"""
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size=Config.COLORBAR_WIDTH, pad=Config.COLORBAR_PAD)
        cbar = plt.colorbar(scatter, cax=cax, extend="both")
        cbar.set_label("Density", fontsize=Config.COLORBAR_LABEL_SIZE, family=Config.FONT_FAMILY)
        cbar.ax.tick_params(labelsize=Config.COLORBAR_TICK_SIZE)
        
        for label in cbar.ax.yaxis.get_ticklabels():
            label.set_family(Config.FONT_FAMILY)
    
    def add_regression_line(self, ax: plt.Axes, slope: float, intercept: float, equation: str):
        """添加回归线"""
        x_line = np.linspace(0, 1, 500)
        y_line = slope * x_line + intercept
        ax.plot(x_line, y_line, "k-", lw=1.5, label=f"Regression: {equation}", zorder=5)
    
    def add_reference_line(self, ax: plt.Axes):
        """添加1:1参考线"""
        ax.plot([0, 1], [0, 1], "k--", lw=1.5, label="1:1 Line", alpha=0.5)
    
    def format_axes(self, ax: plt.Axes):
        """格式化坐标轴"""
        # 设置范围和刻度
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_xticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        ax.tick_params(axis="both", which="major", labelsize=Config.AXIS_TICK_SIZE)
        
        # 设置标签
        ax.set_xlabel(Config.X_COLUMN, fontsize=Config.AXIS_LABEL_SIZE, family=Config.FONT_FAMILY)
        ax.set_ylabel(Config.Y_LABEL, fontsize=Config.AXIS_LABEL_SIZE, family=Config.FONT_FAMILY)
        
        # 设置等比例
        ax.set_aspect("equal", adjustable="box")
        ax.grid(True, alpha=0.3, linestyle='--')
    
    def add_text_annotations(self, ax: plt.Axes, r2: float, equation: str):
        """添加文本标注"""
        ax.text(0.1, 0.92, f"$R^2 = {r2:.3f}$",
                transform=ax.transAxes,
                fontsize=Config.TEXT_SIZE,
                family=Config.FONT_FAMILY,
                verticalalignment="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        ax.text(0.1, 0.85, equation,
                transform=ax.transAxes,
                fontsize=Config.TEXT_SIZE,
                family=Config.FONT_FAMILY,
                verticalalignment="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))


# ============== 主函数 ==============
def main():
    """主程序入口"""
    try:
        # 初始化
        setup_matplotlib()
        print(f"开始数据处理... (使用 {Config.N_CORES} 个CPU核心)")
        
        # 创建进度条
        with tqdm(total=100, desc="总体进度", unit="%") as pbar:
            
            # 1. 加载数据 (10%)
            pbar.set_description("加载数据")
            max_samples = Config.MAX_SAMPLE_SIZE if Config.MEMORY_LIMIT else None
            x, y = load_and_preprocess_data(
                Config.INPUT_FILE, 
                Config.X_COLUMN, 
                Config.Y_COLUMN,
                max_samples
            )
            pbar.update(10)
            
            # 2. 计算评估指标 (20%)
            pbar.set_description("计算评估指标")
            metrics = MetricsCalculator.calculate_all_metrics(x, y, use_parallel=True)
            pbar.update(20)
            
            # 3. 线性回归 (10%)
            pbar.set_description("进行线性回归")
            slope, intercept, r_value, p_value, std_err = linregress(x, y)
            r2 = r_value ** 2
            equation = f"y = {slope:.4f}x + {intercept:.4f}"
            pbar.update(10)
            
            # 4. 计算密度 (20%)
            pbar.set_description("计算散点密度")
            density = DensityCalculator.calculate_adaptive(x, y)
            pbar.update(20)
            
            # 5. 创建图形 (30%)
            pbar.set_description("创建图形")
            plotter = PlotGenerator(x, y, density)
            fig, ax = plotter.create_figure()
            
            scatter = plotter.plot_scatter(ax)
            plotter.add_colorbar(ax, scatter)
            plotter.add_reference_line(ax)
            plotter.add_regression_line(ax, slope, intercept, equation)
            plotter.format_axes(ax)
            plotter.add_text_annotations(ax, r2, equation)
            
            plt.tight_layout()
            pbar.update(30)
            
            # 6. 保存图形 (10%)
            pbar.set_description("保存图形")
            dpi = Config.DPI_OPTIMIZED if Config.MEMORY_LIMIT else Config.DPI_HIGH
            plt.savefig(Config.OUTPUT_FILE, dpi=dpi, bbox_inches="tight", facecolor="white")
            pbar.update(10)
            
            pbar.set_description("完成！")
        
        # 清理资源
        plt.close()
        gc.collect()
        
        # 输出统计信息
        print("\n" + "="*50)
        print("处理完成！")
        print(f"使用CPU核心数: {Config.N_CORES}")
        print(f"内存优化模式: {'开启' if Config.MEMORY_LIMIT else '关闭'}")
        print(f"图像分辨率: {dpi} DPI")
        print("\n评估指标:")
        for key, value in metrics.items():
            print(f"  {key.upper()}: {value:.4f}")
        print(f"  R²: {r2:.4f}")
        print("="*50)
        
    except Exception as e:
        print(f"错误：{str(e)}")
        raise


if __name__ == "__main__":
    main()