#!/usr/bin/env python3
"""
测试简化的3D风场箭头效果
"""

import sys
import os
sys.path.append(r'F:\项目\Code\绘图')

from terrain_wind_3d import PlotConfig, ProcessingConfig, WRFBatchProcessor
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_clean_arrows():
    """测试简化清晰的3D箭头效果"""
    
    print("=" * 60)
    print("测试简化清晰的3D风场箭头效果")
    print("=" * 60)
    
    # 配置简化的箭头效果
    plot_config = PlotConfig(
        # 基本设置
        figure_size=(14, 12),
        dpi=300,
        wind_speed_max=8.0,
        
        # 颜色设置
        wind_3d_colormap="jet",
        
        # 启用简化的3D风场箭头
        enable_3d_wind_arrows=True,
        
        # 只生成3D地形图
        enable_xoy_wind=False,
        enable_3d_terrain=True,
        enable_terrain_overlay=False,
        enable_temperature_overlay=False,
        
        # 设置采样步长（会被代码进一步放大）
        wind_arrow_step=2,  # 基础步长，实际会*6
        terrain_3d_step=3,
        
        # 3D视角优化
        elevation_angle=30.0,
        azimuth_angle=45.0,
    )
    
    processing_config = ProcessingConfig(
        max_memory_usage_gb=4.0,
        enable_parallel=False,
        validate_data=True,
    )
    
    # 文件路径
    wrf_pattern = r"E:\wrf_out5\wrfout_d04_2022*"
    output_dir = r"F:\项目\clean_arrows_output"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        processor = WRFBatchProcessor(plot_config, processing_config)
        
        print(f"WRF文件模式: {wrf_pattern}")
        print(f"输出目录: {output_dir}")
        print("新特性:")
        print("  - 大幅减少箭头数量")
        print("  - 只显示风速>2.0m/s的区域")
        print("  - 统一箭头长度，避免杂乱")
        print("  - 突出显示前3个最大风速位置")
        print("  - 红色主导风向箭头+风速标签")
        print("-" * 60)
        
        # 处理少量文件测试
        import glob
        wrf_files = glob.glob(wrf_pattern)[:1]  # 只处理1个文件快速测试
        
        if not wrf_files:
            print("❌ 未找到WRF文件")
            return False
            
        print(f"测试文件: {os.path.basename(wrf_files[0])}")
        
        result = processor._process_single_file(wrf_files[0], output_dir)
        
        if result:
            print("\n✅ 简化箭头效果生成成功！")
            for output_file in result:
                if output_file:
                    print(f"   📸 输出图片: {os.path.basename(output_file)}")
            
            print(f"\n🎯 检查结果: {output_dir}")
            print("\n期望效果:")
            print("  ✓ 箭头数量明显减少")
            print("  ✓ 只显示强风区域的清晰箭头")
            print("  ✓ 红色主导风向箭头突出显示")
            print("  ✓ 整体视觉效果清晰有序")
        else:
            print("❌ 处理失败")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = test_clean_arrows()
    if success:
        print("\n🎉 简化箭头效果测试完成！")
    else:
        print("\n❌ 测试失败！")