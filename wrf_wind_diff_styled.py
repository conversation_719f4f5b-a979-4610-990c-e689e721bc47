#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WRF风速差值计算与可视化工具
优化版本 - 提供专业的气象数据差值分析功能

主要功能:
- WRF数据提取和处理
- 风速差值计算与统计
- 专业气象可视化
- 多种对比分析图

作者: 优化版本
日期: 2025-09-15
"""

import os
import logging
from dataclasses import dataclass
from typing import Tuple, Optional, Dict
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib import rcParams
from netCDF4 import Dataset
from wrf import getvar, to_np, latlon_coords
import warnings

warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("wrf_wind_diff.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# 设置字体支持
plt.rcParams.update(
    {
        "font.family": ["Times New Roman", "SimHei"],
        "mathtext.fontset": "stix",
        "axes.unicode_minus": False,
        "figure.max_open_warning": 0,
    }
)


@dataclass
class PlotConfig:
    """绘图配置类"""

    # 基本设置
    figure_size: Tuple[int, int] = (12, 10)
    dpi: int = 800
    font_size: int = 12
    title_size: int = 14
    label_size: int = 12

    # 风速设置
    wind_speed_max: float = 15.0
    wind_speed_min: float = 0.0
    wind_levels: int = 256

    # 差值设置
    diff_levels: int = 21
    diff_symmetric: bool = True  # 是否使用对称的差值范围

    # 颜色映射
    wind_colormap: str = "RdYlBu_r"
    diff_colormap: str = "RdBu_r"

    # 采样设置
    wind_arrow_step: int = 10

    # 文件输出设置
    save_bbox_inches: str = "tight"

    # 绘图模块选择
    enable_wind_arrows: bool = True
    enable_contour_lines: bool = True
    enable_statistics: bool = True
    enable_grid: bool = True
    enable_stations: bool = True  # 是否显示气象站点

    # 气象站设置
    station_size: float = 100.0
    station_color: str = "red"
    station_marker: str = "^"  # 三角形标记

    # 实际气象站点信息
    real_stations = [
        {
            "name": "30M",
            "lon": 104.055,
            "lat": 32.9887,
        },
        {
            "name": "75M",
            "lon": 104.016,
            "lat": 32.9998,
        },
    ]

    # 绘图范围设置
    plot_bounds = {
        "min_lon": 103.934,
        "max_lon": 104.12,
        "min_lat": 32.92,
        "max_lat": 33.07,
    }

    # 是否应用范围限制
    apply_bounds: bool = True


class WRFDataExtractor:
    """WRF数据提取器类 - 简化版不依赖wrf-python"""

    def __init__(self):
        pass

    def read_wrf_wind(
        self, filename: str, time_idx: int = 0, level: int = 0
    ) -> Optional[Dict]:
        """
        从WRF输出文件中提取风场和地形数据

        Args:
            filename: WRF文件路径
            time_idx: 时间索引
            level: 垂直层次索引

        Returns:
            包含风场和地形数据的字典
        """
        try:
            logger.info(f"开始读取WRF数据: {filename}")
            ncfile = Dataset(filename)

            # 读取地形高度数据
            hgt = None
            try:
                hgt = getvar(ncfile, "HGT", timeidx=time_idx)
                hgt = to_np(hgt)
                logger.info(f"成功读取地形数据 HGT, 形状: {hgt.shape}")
                logger.info(f"地形高度范围: {hgt.min():.1f} - {hgt.max():.1f} m")
            except Exception as e:
                logger.warning(f"未找到HGT变量: {e}")

            # 尝试读取10米风场
            try:
                u10 = getvar(ncfile, "U10", timeidx=time_idx)
                v10 = getvar(ncfile, "V10", timeidx=time_idx)
                wspd10 = np.sqrt(u10**2 + v10**2)

                # 获取经纬度坐标
                lats, lons = latlon_coords(u10)

                ncfile.close()
                logger.info("成功读取10米风场数据")

                return {
                    "u": to_np(u10),
                    "v": to_np(v10),
                    "wspd": to_np(wspd10),
                    "lat": to_np(lats),
                    "lon": to_np(lons),
                    "hgt": hgt,  # 添加地形数据
                    "height": "10m",
                }
            except Exception as e:
                logger.info(f"未找到10米风场: {e}")
                logger.info("尝试读取模式层风场...")

            # 读取模式层风场
            u = getvar(ncfile, "ua", timeidx=time_idx)
            v = getvar(ncfile, "va", timeidx=time_idx)

            # 选择特定层次
            if len(u.shape) == 3:
                u = u[level, :, :]
                v = v[level, :, :]

            wspd = np.sqrt(u**2 + v**2)

            # 获取经纬度坐标
            lats, lons = latlon_coords(u)

            ncfile.close()
            logger.info(f"成功读取模式层{level}风场数据")

            return {
                "u": to_np(u),
                "v": to_np(v),
                "wspd": to_np(wspd),
                "lat": to_np(lats),
                "lon": to_np(lons),
                "hgt": hgt,  # 添加地形数据
                "height": f"Level {level}",
            }

        except Exception as e:
            logger.error(f"读取WRF数据时出错: {e}")
            if "ncfile" in locals():
                ncfile.close()
            return None

    def calculate_wind_difference(self, wrf1_data: Dict, wrf2_data: Dict) -> Dict:
        """
        计算两个WRF文件的风速差值

        Args:
            wrf1_data: 第一个WRF文件的风场数据
            wrf2_data: 第二个WRF文件的风场数据

        Returns:
            差值数据字典
        """
        try:
            diff_data = {
                "u_diff": wrf1_data["u"] - wrf2_data["u"],
                "v_diff": wrf1_data["v"] - wrf2_data["v"],
                "wspd_diff": wrf1_data["wspd"] - wrf2_data["wspd"],
                "lat": wrf1_data["lat"],
                "lon": wrf1_data["lon"],
            }

            # 计算统计信息
            diff_data["stats"] = self._calculate_statistics(diff_data["wspd_diff"])

            return diff_data

        except Exception as e:
            logger.error(f"计算风速差值时出错: {e}")
            raise

    def _calculate_statistics(self, data: np.ndarray) -> Dict:
        """计算统计信息"""
        return {
            "max": np.max(data),
            "min": np.min(data),
            "mean": np.mean(data),
            "std": np.std(data),
            "abs_max": np.max(np.abs(data)),
        }

    def calculate_comprehensive_statistics(
        self,
        wrf1_data: Dict,
        wrf2_data: Dict,
        diff_data: Dict,
        config: PlotConfig = None,
    ) -> Dict:
        """
        计算综合统计信息，包括风速差值正负占比和各海拔高度的统计

        Args:
            wrf1_data: 第一个WRF文件的数据
            wrf2_data: 第二个WRF文件的数据
            diff_data: 差值数据
            config: 绘图配置（可选）

        Returns:
            包含各种统计信息的字典
        """
        try:
            wspd_diff = diff_data["wspd_diff"]

            # 基本统计
            total_points = wspd_diff.size
            positive_mask = wspd_diff > 0
            negative_mask = wspd_diff < 0
            zero_mask = np.abs(wspd_diff) < 0.01  # 接近零的值

            positive_count = np.sum(positive_mask)
            negative_count = np.sum(negative_mask)
            zero_count = np.sum(zero_mask)

            # 计算占比
            positive_ratio = positive_count / total_points * 100
            negative_ratio = negative_count / total_points * 100
            zero_ratio = zero_count / total_points * 100

            # 计算平均差值
            mean_diff = np.mean(wspd_diff)
            mean_positive = (
                np.mean(wspd_diff[positive_mask]) if positive_count > 0 else 0
            )
            mean_negative = (
                np.mean(wspd_diff[negative_mask]) if negative_count > 0 else 0
            )

            # 计算各象限的平均差值（如果有边界限制）
            if config and hasattr(config, "apply_bounds") and config.apply_bounds:
                lon = diff_data["lon"]
                lat = diff_data["lat"]
                bounds = config.plot_bounds

                # 应用边界掩码
                lon_mask = (lon >= bounds["min_lon"]) & (lon <= bounds["max_lon"])
                lat_mask = (lat >= bounds["min_lat"]) & (lat <= bounds["max_lat"])
                bounds_mask = lon_mask & lat_mask

                wspd_diff_bounded = wspd_diff[bounds_mask]
                mean_diff_bounded = (
                    np.mean(wspd_diff_bounded) if wspd_diff_bounded.size > 0 else 0
                )
            else:
                mean_diff_bounded = mean_diff

            statistics = {
                "basic": {
                    "total_points": total_points,
                    "positive_count": positive_count,
                    "negative_count": negative_count,
                    "zero_count": zero_count,
                    "positive_ratio": positive_ratio,
                    "negative_ratio": negative_ratio,
                    "zero_ratio": zero_ratio,
                },
                "mean_values": {
                    "mean_diff": mean_diff,
                    "mean_diff_bounded": mean_diff_bounded,
                    "mean_positive": mean_positive,
                    "mean_negative": mean_negative,
                    "mean_wrf1": np.mean(wrf1_data["wspd"]),
                    "mean_wrf2": np.mean(wrf2_data["wspd"]),
                },
                "extreme_values": {
                    "max_diff": np.max(wspd_diff),
                    "min_diff": np.min(wspd_diff),
                    "abs_max_diff": np.max(np.abs(wspd_diff)),
                    "std_diff": np.std(wspd_diff),
                },
            }

            # 如果有地形数据，计算各海拔高度的统计
            if wrf1_data.get("hgt") is not None:
                hgt = wrf1_data["hgt"]
                statistics["elevation"] = self._calculate_elevation_statistics(
                    hgt, wspd_diff
                )

            return statistics

        except Exception as e:
            logger.error(f"计算综合统计信息时出错: {e}")
            raise

    def _calculate_elevation_statistics(
        self, hgt: np.ndarray, wspd_diff: np.ndarray
    ) -> Dict:
        """
        计算各海拔高度段的风速差值统计

        Args:
            hgt: 地形高度数据
            wspd_diff: 风速差值数据

        Returns:
            各海拔段的统计信息
        """
        try:
            # 定义海拔区间
            min_hgt = np.min(hgt)
            max_hgt = np.max(hgt)

            # 创建海拔分段（每500米一段）
            elevation_bins = np.arange(
                np.floor(min_hgt / 500) * 500, np.ceil(max_hgt / 500) * 500 + 500, 500
            )

            if len(elevation_bins) < 2:
                # 如果高度范围太小，使用更细的分段
                elevation_bins = np.arange(
                    np.floor(min_hgt / 100) * 100,
                    np.ceil(max_hgt / 100) * 100 + 100,
                    100,
                )

            elevation_stats = []

            for i in range(len(elevation_bins) - 1):
                lower_bound = elevation_bins[i]
                upper_bound = elevation_bins[i + 1]

                # 找到该海拔区间的点
                mask = (hgt >= lower_bound) & (hgt < upper_bound)

                if np.sum(mask) > 0:
                    diff_in_bin = wspd_diff[mask]

                    positive_count = np.sum(diff_in_bin > 0)
                    negative_count = np.sum(diff_in_bin < 0)
                    total_in_bin = len(diff_in_bin)

                    stats_dict = {
                        "elevation_range": f"{lower_bound:.0f}-{upper_bound:.0f}m",
                        "lower_bound": lower_bound,
                        "upper_bound": upper_bound,
                        "point_count": total_in_bin,
                        "mean_diff": np.mean(diff_in_bin),
                        "std_diff": np.std(diff_in_bin),
                        "max_diff": np.max(diff_in_bin),
                        "min_diff": np.min(diff_in_bin),
                        "positive_ratio": positive_count / total_in_bin * 100,
                        "negative_ratio": negative_count / total_in_bin * 100,
                        "mean_elevation": np.mean(hgt[mask]),
                    }

                    elevation_stats.append(stats_dict)

            return {
                "bins": elevation_stats,
                "summary": {
                    "num_bins": len(elevation_stats),
                    "min_elevation": min_hgt,
                    "max_elevation": max_hgt,
                    "elevation_range": max_hgt - min_hgt,
                },
            }

        except Exception as e:
            logger.error(f"计算海拔统计信息时出错: {e}")
            return {}


class WindDifferencePlotter:
    """风速差值绘图器类"""

    def __init__(self, config: PlotConfig = None):
        self.config = config or PlotConfig()

    def _apply_plot_bounds(
        self, lon: np.ndarray, lat: np.ndarray, *data_arrays
    ) -> tuple:
        """应用绘图范围限制"""
        if not self.config.apply_bounds:
            result = [lon, lat]
            result.extend(data_arrays)
            return tuple(result)

        bounds = self.config.plot_bounds

        # 创建掩码
        lon_mask = (lon >= bounds["min_lon"]) & (lon <= bounds["max_lon"])
        lat_mask = (lat >= bounds["min_lat"]) & (lat <= bounds["max_lat"])
        mask = lon_mask & lat_mask

        # 找到有效区域的边界
        valid_rows, valid_cols = np.where(mask)
        if len(valid_rows) == 0:
            logger.warning("指定范围内没有有效数据点")
            result = [lon, lat]
            result.extend(data_arrays)
            return tuple(result)

        row_min, row_max = valid_rows.min(), valid_rows.max() + 1
        col_min, col_max = valid_cols.min(), valid_cols.max() + 1

        # 裁剪所有数组
        result = []
        result.append(lon[row_min:row_max, col_min:col_max])
        result.append(lat[row_min:row_max, col_min:col_max])

        for data in data_arrays:
            if data.ndim == 2:
                result.append(data[row_min:row_max, col_min:col_max])
            else:
                result.append(data)

        return tuple(result)

    def _add_weather_stations(self, ax):
        """添加气象站点到图上"""
        if not self.config.enable_stations:
            return

        for station in self.config.real_stations:
            # 绘制站点
            ax.scatter(
                station["lon"],
                station["lat"],
                c=self.config.station_color,
                s=self.config.station_size,
                marker=self.config.station_marker,
                edgecolors="white",
                linewidth=2,
                alpha=1.0,
                zorder=1000,  # 确保在最上层
            )

            # 添加站点标签
            ax.annotate(
                station["name"],
                (station["lon"], station["lat"]),
                xytext=(5, 5),
                textcoords="offset points",
                fontsize=10,
                color=self.config.station_color,
                fontweight="bold",
                bbox=dict(
                    boxstyle="round,pad=0.3",
                    facecolor="white",
                    alpha=0.8,
                    edgecolor=self.config.station_color,
                ),
                zorder=1001,
            )

    def _setup_figure(
        self, figsize: Tuple[int, int] = None
    ) -> Tuple[plt.Figure, plt.Axes]:
        """设置图形和坐标轴"""
        figsize = figsize or self.config.figure_size

        plt.rcParams.update(
            {
                "figure.figsize": figsize,
                "font.size": self.config.font_size,
                "axes.titlesize": self.config.title_size,
                "axes.labelsize": self.config.label_size,
            }
        )

        fig, ax = plt.subplots(figsize=figsize)
        return fig, ax

    def _create_wind_colormap(self, data: np.ndarray = None):
        """创建风速颜色映射"""
        # 如果提供了数据，使用实际数据范围
        if data is not None:
            vmin = 0  # 风速最小值始终为0
            vmax = np.max(data) * 1.05  # 稍微扩大最大值，避免边界问题
        else:
            vmin = self.config.wind_speed_min
            vmax = self.config.wind_speed_max

        wind_levels = np.linspace(vmin, vmax, self.config.wind_levels)

        try:
            wind_cmap = plt.colormaps.get_cmap(self.config.wind_colormap)
        except AttributeError:
            wind_cmap = plt.cm.get_cmap(self.config.wind_colormap)

        wind_norm = colors.Normalize(vmin=vmin, vmax=vmax)

        return wind_levels, wind_cmap, wind_norm

    def _create_diff_colormap(self, data: np.ndarray):
        """创建差值颜色映射"""
        if self.config.diff_symmetric:
            vmax = np.abs(data).max()
            vmin = -vmax
        else:
            vmax = data.max()
            vmin = data.min()

        levels = np.linspace(vmin, vmax, self.config.diff_levels)

        try:
            cmap = plt.colormaps.get_cmap(self.config.diff_colormap)
        except AttributeError:
            cmap = plt.cm.get_cmap(self.config.diff_colormap)

        norm = colors.Normalize(vmin=vmin, vmax=vmax)

        return levels, cmap, norm

    def _create_terrain_colormap(self, data: np.ndarray = None):
        """创建地形高度颜色映射"""
        if data is not None:
            vmin = np.nanmin(data)
            vmax = np.nanmax(data)
        else:
            vmin = 0
            vmax = 5000  # 默认最大高度5000米

        # 使用更多层次的地形专用色标
        levels = np.linspace(vmin, vmax, 40)  # 增加到40个层次

        # 创建更丰富的地形色标（从低到高：深蓝-绿-黄-橙-棕-红-白）
        terrain_colors = [
            "#08306b",  # 深蓝（最低海拔）
            "#08519c",  # 蓝
            "#2171b5",  # 浅蓝
            "#4292c6",  # 亮蓝
            "#6baed6",  # 天蓝
            "#1a9850",  # 深绿
            "#66c2a5",  # 绿
            "#a6d96a",  # 浅绿
            "#d9ef8b",  # 黄绿
            "#fee08b",  # 浅黄
            "#fdae61",  # 橙黄
            "#f46d43",  # 橙
            "#d73027",  # 红橙
            "#a50026",  # 深红
            "#8b4513",  # 棕
            "#6d4c41",  # 深棕
            "#bcaaa4",  # 浅棕
            "#d7ccc8",  # 灰棕
            "#efebe9",  # 浅灰白
            "#fafafa",  # 灰白
            "#ffffff",  # 纯白（雪山）
        ]

        try:
            from matplotlib.colors import LinearSegmentedColormap

            # 创建更平滑的渐变
            cmap = LinearSegmentedColormap.from_list(
                "terrain_enhanced", terrain_colors, N=512
            )
        except:
            # 备用方案：使用matplotlib内置的terrain色标
            cmap = plt.cm.get_cmap("terrain")

        # 使用PowerNorm来增强中低海拔的对比度
        from matplotlib.colors import PowerNorm

        # gamma < 1 会增强低值区域的对比度
        norm = PowerNorm(gamma=0.7, vmin=vmin, vmax=vmax)

        return levels, cmap, norm

    def _save_figure(self, fig: plt.Figure, filename: str) -> str:
        """保存图形"""
        try:
            fig.savefig(
                filename, dpi=self.config.dpi, bbox_inches=self.config.save_bbox_inches
            )
            logger.info(f"图片已保存: {filename}")
            plt.close(fig)
            return filename
        except Exception as e:
            logger.error(f"保存图片时出错: {e}")
            raise

    def plot_wind_difference(
        self,
        diff_data: Dict,
        wrf1_file: str,
        wrf2_file: str,
        save_path: Optional[str] = None,
    ) -> str:
        """
        绘制风速差值图

        Args:
            diff_data: 差值数据
            wrf1_file: 第一个文件名
            wrf2_file: 第二个文件名
            save_path: 图片保存路径

        Returns:
            保存的文件路径
        """
        try:
            logger.info("开始绘制风速差值图")

            # 应用绘图范围限制
            lon = diff_data["lon"]
            lat = diff_data["lat"]
            wspd_diff = diff_data["wspd_diff"]
            u_diff = diff_data["u_diff"]
            v_diff = diff_data["v_diff"]

            if self.config.apply_bounds:
                lon, lat, wspd_diff, u_diff, v_diff = self._apply_plot_bounds(
                    lon, lat, wspd_diff, u_diff, v_diff
                )

            fig, ax = self._setup_figure()

            # 设置绘图范围
            if self.config.apply_bounds:
                bounds = self.config.plot_bounds
                ax.set_xlim(bounds["min_lon"], bounds["max_lon"])
                ax.set_ylim(bounds["min_lat"], bounds["max_lat"])
            else:
                ax.set_xlim(lon.min(), lon.max())
                ax.set_ylim(lat.min(), lat.max())

            # 创建颜色映射
            levels, cmap, norm = self._create_diff_colormap(wspd_diff)

            # 绘制填充等值线
            cf = ax.contourf(
                lon,
                lat,
                wspd_diff,
                levels=levels,
                cmap=cmap,
                norm=norm,
                extend="both",
                alpha=0.9,
            )

            # 添加等值线
            if self.config.enable_contour_lines:
                cs = ax.contour(
                    lon,
                    lat,
                    wspd_diff,
                    levels=levels[::2],
                    colors="black",
                    linewidths=0.8,
                    alpha=0.6,
                )
                ax.clabel(cs, inline=True, fontsize=9, fmt="%.1f")

            # 绘制风矢量差值
            if self.config.enable_wind_arrows:
                skip = self.config.wind_arrow_step
                u_diff_skip = u_diff[::skip, ::skip]
                v_diff_skip = v_diff[::skip, ::skip]
                lon_skip = lon[::skip, ::skip]
                lat_skip = lat[::skip, ::skip]

                qv = ax.quiver(
                    lon_skip,
                    lat_skip,
                    u_diff_skip,
                    v_diff_skip,
                    scale=120,
                    scale_units="inches",
                    width=0.003,
                    headwidth=3,
                    headlength=4,
                    color="white",
                    edgecolor="black",
                    linewidth=0.5,
                    alpha=0.8,
                )

                # 添加风矢量图例
                ax.quiverkey(
                    qv,
                    0.9,
                    0.95,
                    5,
                    "5 m/s",
                    coordinates="axes",
                    fontproperties={"size": 10},
                    color="white",
                    labelcolor="black",
                )

            # 添加气象站点
            self._add_weather_stations(ax)

            # 添加颜色条
            cbar = fig.colorbar(cf, ax=ax, orientation="vertical", pad=0.02, shrink=0.8)
            cbar.set_label(
                "风速差值 (m/s)",
                rotation=270,
                labelpad=20,
                fontsize=self.config.label_size,
            )
            cbar.ax.tick_params(labelsize=10)

            # 设置颜色条刻度
            if "stats" in diff_data:
                max_val = diff_data["stats"]["abs_max"]
                cbar.set_ticks(np.linspace(-max_val, max_val, 9))

            # 设置坐标轴标签
            ax.set_xlabel("经度 (°)", fontsize=self.config.label_size)
            ax.set_ylabel("纬度 (°)", fontsize=self.config.label_size)

            # 添加网格
            if self.config.enable_grid:
                ax.grid(True, linestyle="--", alpha=0.3, linewidth=0.5)

            # 设置标题
            file1_name = os.path.basename(wrf1_file)
            file2_name = os.path.basename(wrf2_file)
            ax.set_title(
                f"风速差值分析\n({file2_name} - {file1_name})",
                fontsize=self.config.title_size,
                fontweight="bold",
                pad=20,
            )

            # 添加统计信息文本框
            if self.config.enable_statistics and "stats" in diff_data:
                stats = diff_data["stats"]
                stats_text = (
                    f"最大正差值: {stats['max']:.2f} m/s\n"
                    f"最大负差值: {stats['min']:.2f} m/s\n"
                    f"平均差值: {stats['mean']:.2f} m/s\n"
                    f"标准差: {stats['std']:.2f} m/s"
                )
                ax.text(
                    0.02,
                    0.98,
                    stats_text,
                    transform=ax.transAxes,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                    verticalalignment="top",
                    fontsize=10,
                )

            # 设置坐标轴范围以保持等比例
            ax.set_aspect("equal", adjustable="box")

            plt.tight_layout()

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制风速差值图时出错: {e}")
            raise

    def plot_comparison(
        self,
        wrf1_data: Dict,
        wrf2_data: Dict,
        diff_data: Dict,
        wrf1_file: str,
        wrf2_file: str,
        save_path: Optional[str] = None,
    ) -> str:
        """
        绘制对比图（三个子图）
        """
        try:
            logger.info("开始绘制对比图")

            fig, axes = plt.subplots(1, 3, figsize=(18, 6))

            # 应用范围限制到所有数据
            if self.config.apply_bounds:
                wrf1_lon, wrf1_lat, wrf1_wspd = self._apply_plot_bounds(
                    wrf1_data["lon"], wrf1_data["lat"], wrf1_data["wspd"]
                )
                wrf2_lon, wrf2_lat, wrf2_wspd = self._apply_plot_bounds(
                    wrf2_data["lon"], wrf2_data["lat"], wrf2_data["wspd"]
                )
                diff_lon, diff_lat, diff_wspd = self._apply_plot_bounds(
                    diff_data["lon"], diff_data["lat"], diff_data["wspd_diff"]
                )

                # 更新数据
                wrf1_data_plot = {"lon": wrf1_lon, "lat": wrf1_lat, "wspd": wrf1_wspd}
                wrf2_data_plot = {"lon": wrf2_lon, "lat": wrf2_lat, "wspd": wrf2_wspd}
                diff_data_plot = {
                    "lon": diff_lon,
                    "lat": diff_lat,
                    "wspd_diff": diff_wspd,
                }
            else:
                wrf1_data_plot = wrf1_data
                wrf2_data_plot = wrf2_data
                diff_data_plot = diff_data

            # 数据列表
            data_list = [wrf1_data_plot, wrf2_data_plot, diff_data_plot]
            file1_name = os.path.basename(wrf1_file)
            file2_name = os.path.basename(wrf2_file)
            titles = [
                f"YSU_SMAG",
                f"YSU_SMAG_NBA",
                "风速差值 (YSU_SMAG - YSU_SMAG_NBA)",  # 与实际计算一致：WRF1 - WRF2
            ]

            # 计算所有WRF数据的统一最大值，确保色标一致
            all_wspd_max = max(
                wrf1_data_plot["wspd"].max(), wrf2_data_plot["wspd"].max()
            )

            for idx, (ax, data, title) in enumerate(zip(axes, data_list, titles)):
                # 设置绘图范围
                if self.config.apply_bounds:
                    bounds = self.config.plot_bounds
                    ax.set_xlim(bounds["min_lon"], bounds["max_lon"])
                    ax.set_ylim(bounds["min_lat"], bounds["max_lat"])
                else:
                    ax.set_xlim(data["lon"].min(), data["lon"].max())
                    ax.set_ylim(data["lat"].min(), data["lat"].max())

                # 选择要绘制的数据和颜色映射
                if idx < 2:  # WRF原始数据
                    plot_data = data["wspd"]
                    # 使用统一的最大值创建色标，避免突变
                    vmin = 0
                    vmax = all_wspd_max * 1.05  # 稍微扩大范围
                    levels = np.linspace(vmin, vmax, 20)
                    try:
                        cmap = plt.colormaps.get_cmap(self.config.wind_colormap)
                    except AttributeError:
                        cmap = plt.cm.get_cmap(self.config.wind_colormap)
                    norm = colors.Normalize(vmin=vmin, vmax=vmax)
                    label = "风速 (m/s)"
                else:  # 差值数据
                    plot_data = data["wspd_diff"]
                    levels, cmap, norm = self._create_diff_colormap(plot_data)
                    label = "风速差值 (m/s)"

                # 绘制填充等值线
                cf = ax.contourf(
                    data["lon"],
                    data["lat"],
                    plot_data,
                    levels=levels,  # 使用统一的levels
                    cmap=cmap,
                    norm=norm,
                    extend="both",
                    alpha=0.9,
                )

                # 添加等值线
                if self.config.enable_contour_lines:
                    # 为等值线选择合适的级别
                    contour_levels = levels[::2] if idx < 2 else levels[::3]
                    cs = ax.contour(
                        data["lon"],
                        data["lat"],
                        plot_data,
                        levels=contour_levels,
                        colors="black",
                        linewidths=0.5,
                        alpha=0.4,
                    )
                    ax.clabel(cs, inline=True, fontsize=8, fmt="%.1f")

                # 添加气象站点
                self._add_weather_stations(ax)

                # 添加颜色条
                cbar = plt.colorbar(
                    cf, ax=ax, orientation="horizontal", pad=0.1, shrink=0.8, aspect=20
                )
                cbar.set_label(label, fontsize=10)
                cbar.ax.tick_params(labelsize=9)

                # 设置颜色条刻度
                if idx < 2:  # WRF原始数据使用相同的刻度
                    # 设置统一的刻度，从0到最大值
                    n_ticks = 11  # 显示11个刻度
                    tick_values = np.linspace(0, vmax, n_ticks)
                    cbar.set_ticks(tick_values)
                    cbar.set_ticklabels([f"{v:.1f}" for v in tick_values])

                # 设置坐标轴
                ax.set_xlabel("经度 (°)", fontsize=10)
                ax.set_ylabel("纬度 (°)", fontsize=10)
                ax.set_aspect("equal", adjustable="box")

                # 添加网格
                if self.config.enable_grid:
                    ax.grid(True, linestyle="--", alpha=0.3, linewidth=0.5)

                # 设置标题
                ax.set_title(title, fontsize=11, fontweight="bold", pad=10)

            # 添加总标题
            """fig.suptitle(
                "WRF风场对比分析",
                fontsize=self.config.title_size,
                fontweight="bold",
                y=1.02,
            )"""

            plt.tight_layout()

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制对比图时出错: {e}")
            raise

    def plot_terrain(
        self, wrf_data: Dict, save_path: Optional[str] = None, show_wind: bool = True
    ) -> str:
        """
        绘制地形图

        Args:
            wrf_data: WRF数据字典（包含hgt地形数据）
            save_path: 保存路径
            show_wind: 是否叠加显示风场

        Returns:
            保存的文件路径
        """
        try:
            logger.info("开始绘制地形图")

            # 检查是否有地形数据
            if wrf_data.get("hgt") is None:
                logger.warning("WRF数据中没有地形高度信息")
                return ""

            # 应用绘图范围限制
            lon = wrf_data["lon"]
            lat = wrf_data["lat"]
            hgt = wrf_data["hgt"]
            wspd = wrf_data.get("wspd", None)
            u = wrf_data.get("u", None)
            v = wrf_data.get("v", None)

            if self.config.apply_bounds:
                if wspd is not None and u is not None and v is not None:
                    lon, lat, hgt, wspd, u, v = self._apply_plot_bounds(
                        lon, lat, hgt, wspd, u, v
                    )
                else:
                    lon, lat, hgt = self._apply_plot_bounds(lon, lat, hgt)

            fig, ax = self._setup_figure()

            # 设置绘图范围
            if self.config.apply_bounds:
                bounds = self.config.plot_bounds
                ax.set_xlim(bounds["min_lon"], bounds["max_lon"])
                ax.set_ylim(bounds["min_lat"], bounds["max_lat"])
            else:
                ax.set_xlim(lon.min(), lon.max())
                ax.set_ylim(lat.min(), lat.max())

            # 创建地形颜色映射
            levels, cmap, norm = self._create_terrain_colormap(hgt)

            # 添加山体阴影效果（可选）
            from matplotlib.colors import LightSource

            ls = LightSource(azdeg=315, altdeg=45)
            # 创建阴影数据
            hillshade = ls.hillshade(hgt, vert_exag=2.0, dx=1, dy=1)

            # 先绘制山体阴影
            ax.contourf(
                lon,
                lat,
                hillshade,
                levels=100,
                cmap="gray",
                alpha=0.3,
                antialiased=True,
            )

            # 绘制地形填充等值线
            cf = ax.contourf(
                lon,
                lat,
                hgt,
                levels=levels,
                cmap=cmap,
                norm=norm,
                extend="both",
                alpha=0.85,  # 稍微降低透明度以显示阴影
            )

            # 添加地形等值线
            # 根据高度范围动态调整等高线间隔
            height_range = hgt.max() - hgt.min()
            if height_range < 500:
                contour_interval = 50  # 每50米一条
            elif height_range < 1000:
                contour_interval = 100  # 每100米一条
            elif height_range < 2000:
                contour_interval = 150  # 每150米一条
            else:
                contour_interval = 200  # 每200米一条

            # 创建等高线级别
            min_contour = np.ceil(hgt.min() / contour_interval) * contour_interval
            max_contour = np.floor(hgt.max() / contour_interval) * contour_interval
            contour_levels = np.arange(
                min_contour, max_contour + contour_interval, contour_interval
            )

            cs = ax.contour(
                lon,
                lat,
                hgt,
                levels=contour_levels,
                colors="black",
                linewidths=0.4,
                alpha=0.6,
            )
            # 标注主要等高线（每隔一条标注）
            clabel_levels = (
                contour_levels[::2] if len(contour_levels) > 10 else contour_levels
            )
            ax.clabel(cs, levels=clabel_levels, inline=True, fontsize=7, fmt="%.0f m")

            # 如果需要叠加风场
            if show_wind and wspd is not None:
                # 添加风速等值线
                wind_cs = ax.contour(
                    lon,
                    lat,
                    wspd,
                    levels=np.arange(0, wspd.max(), 2),  # 每2m/s一条
                    colors="blue",
                    linewidths=0.5,
                    alpha=0.7,
                    linestyles="--",
                )
                ax.clabel(
                    wind_cs, inline=True, fontsize=7, fmt="%.0f m/s", colors="blue"
                )

                # 添加风矢量
                if self.config.enable_wind_arrows and u is not None and v is not None:
                    skip = self.config.wind_arrow_step
                    Q = ax.quiver(
                        lon[::skip, ::skip],
                        lat[::skip, ::skip],
                        u[::skip, ::skip],
                        v[::skip, ::skip],
                        scale=200,
                        scale_units="xy",
                        angles="xy",
                        color="darkblue",
                        alpha=0.7,
                        width=0.002,
                    )
                    ax.quiverkey(
                        Q,
                        0.9,
                        0.95,
                        10,
                        "10 m/s",
                        labelpos="E",
                        coordinates="axes",
                        fontproperties={"size": 9},
                    )

            # 添加气象站点
            self._add_weather_stations(ax)

            # 添加颜色条
            cbar = plt.colorbar(
                cf,
                ax=ax,
                orientation="horizontal",
                pad=0.1,
                shrink=0.8,
                aspect=20,
                extend="both",  # 显示超出范围的值
            )
            cbar.set_label("地形高度 (m)", fontsize=10, fontweight="bold")
            cbar.ax.tick_params(labelsize=9)

            # 设置颜色条刻度
            # 根据高度范围设置合适的刻度
            if height_range < 1000:
                tick_interval = 100
            elif height_range < 2000:
                tick_interval = 200
            elif height_range < 3000:
                tick_interval = 300
            else:
                tick_interval = 500

            tick_values = np.arange(
                np.ceil(hgt.min() / tick_interval) * tick_interval,
                np.floor(hgt.max() / tick_interval) * tick_interval + tick_interval,
                tick_interval,
            )
            cbar.set_ticks(tick_values)
            cbar.set_ticklabels([f"{int(v)}" for v in tick_values])

            # 设置坐标轴
            ax.set_xlabel("经度 (°)", fontsize=10)
            ax.set_ylabel("纬度 (°)", fontsize=10)
            ax.set_aspect("equal", adjustable="box")

            # 添加网格
            if self.config.enable_grid:
                ax.grid(True, linestyle="--", alpha=0.3, linewidth=0.5)

            # 设置标题
            title = "地形高度分布"
            if show_wind and wspd is not None:
                title += " + 风场"
            ax.set_title(title, fontsize=11, fontweight="bold", pad=10)

            # 添加统计信息
            stats_text = f"地形高度: {hgt.min():.0f} - {hgt.max():.0f} m\n"
            stats_text += f"平均高度: {hgt.mean():.0f} m"
            if show_wind and wspd is not None:
                stats_text += f"\n风速范围: {wspd.min():.1f} - {wspd.max():.1f} m/s"

            ax.text(
                0.02,
                0.98,
                stats_text,
                transform=ax.transAxes,
                fontsize=9,
                verticalalignment="top",
                bbox=dict(boxstyle="round,pad=0.5", facecolor="white", alpha=0.8),
            )

            plt.tight_layout()

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制地形图时出错: {e}")
            raise

    def plot_statistics_report(
        self, statistics: Dict, save_path: Optional[str] = None
    ) -> str:
        """
        绘制统计报告图表

        Args:
            statistics: 综合统计信息字典
            save_path: 保存路径

        Returns:
            保存的文件路径
        """
        try:
            logger.info("开始绘制统计报告图表")

            # 创建包含多个子图的图形
            fig = plt.figure(figsize=(16, 12))

            # 1. 风速差值正负占比饼图
            ax1 = plt.subplot(2, 3, 1)
            basic_stats = statistics["basic"]
            sizes = [
                basic_stats["positive_ratio"],
                basic_stats["negative_ratio"],
                basic_stats["zero_ratio"],
            ]
            labels = [
                f'正差值\n({basic_stats["positive_ratio"]:.1f}%)',
                f'负差值\n({basic_stats["negative_ratio"]:.1f}%)',
                f'接近零\n({basic_stats["zero_ratio"]:.1f}%)',
            ]
            colors = ["#ff6b6b", "#4dabf7", "#f8f9fa"]
            explode = (0.05, 0.05, 0)

            ax1.pie(
                sizes,
                explode=explode,
                labels=labels,
                colors=colors,
                autopct="%1.1f%%",
                shadow=True,
                startangle=90,
            )
            ax1.set_title("风速差值正负占比", fontsize=12, fontweight="bold")

            # 2. 平均值对比柱状图
            ax2 = plt.subplot(2, 3, 2)
            mean_stats = statistics["mean_values"]
            categories = [
                "WRF1\n平均",
                "WRF2\n平均",
                "总体\n差值",
                "正差值\n平均",
                "负差值\n平均",
            ]
            values = [
                mean_stats["mean_wrf1"],
                mean_stats["mean_wrf2"],
                mean_stats["mean_diff"],
                mean_stats["mean_positive"],
                mean_stats["mean_negative"],
            ]

            bars = ax2.bar(
                categories,
                values,
                color=["#74c0fc", "#a5d8ff", "#868e96", "#ff6b6b", "#4dabf7"],
            )
            ax2.set_ylabel("风速 (m/s)", fontsize=10)
            ax2.set_title("平均风速对比", fontsize=12, fontweight="bold")
            ax2.grid(True, axis="y", alpha=0.3)

            # 在柱子上添加数值
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax2.text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height,
                    f"{value:.2f}",
                    ha="center",
                    va="bottom",
                    fontsize=9,
                )

            # 3. 极值统计
            ax3 = plt.subplot(2, 3, 3)
            extreme_stats = statistics["extreme_values"]
            extreme_data = {
                "最大正差值": extreme_stats["max_diff"],
                "最大负差值": abs(extreme_stats["min_diff"]),
                "标准差": extreme_stats["std_diff"],
            }

            bars = ax3.bar(
                extreme_data.keys(),
                extreme_data.values(),
                color=["#ff6b6b", "#4dabf7", "#868e96"],
            )
            ax3.set_ylabel("风速差值 (m/s)", fontsize=10)
            ax3.set_title("差值极值统计", fontsize=12, fontweight="bold")
            ax3.grid(True, axis="y", alpha=0.3)

            for bar, value in zip(bars, extreme_data.values()):
                height = bar.get_height()
                ax3.text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height,
                    f"{value:.2f}",
                    ha="center",
                    va="bottom",
                    fontsize=9,
                )

            # 4. 海拔高度分层统计（如果有）
            if "elevation" in statistics:
                ax4 = plt.subplot(2, 3, 4)
                elevation_data = statistics["elevation"]["bins"]

                if elevation_data:
                    elevations = [d["elevation_range"] for d in elevation_data]
                    positive_ratios = [d["positive_ratio"] for d in elevation_data]
                    negative_ratios = [-d["negative_ratio"] for d in elevation_data]

                    x = np.arange(len(elevations))
                    width = 0.35

                    ax4.barh(
                        x - width / 2,
                        positive_ratios,
                        width,
                        label="正差值占比",
                        color="#ff6b6b",
                        alpha=0.8,
                    )
                    ax4.barh(
                        x + width / 2,
                        negative_ratios,
                        width,
                        label="负差值占比",
                        color="#4dabf7",
                        alpha=0.8,
                    )

                    ax4.set_yticks(x)
                    ax4.set_yticklabels(elevations)
                    ax4.set_xlabel("占比 (%)", fontsize=10)
                    ax4.set_title(
                        "各海拔高度正负差值占比", fontsize=12, fontweight="bold"
                    )
                    ax4.legend(loc="best", fontsize=9)
                    ax4.grid(True, axis="x", alpha=0.3)
                    ax4.axvline(x=0, color="black", linestyle="-", linewidth=0.5)

                # 5. 海拔高度平均差值
                ax5 = plt.subplot(2, 3, 5)
                if elevation_data:
                    mean_diffs = [d["mean_diff"] for d in elevation_data]
                    std_diffs = [d["std_diff"] for d in elevation_data]

                    ax5.errorbar(
                        x,
                        mean_diffs,
                        yerr=std_diffs,
                        fmt="o-",
                        color="#495057",
                        capsize=5,
                        capthick=2,
                        label="平均差值±标准差",
                    )

                    ax5.set_xticks(x)
                    ax5.set_xticklabels(
                        [d["elevation_range"] for d in elevation_data],
                        rotation=45,
                        ha="right",
                    )
                    ax5.set_ylabel("风速差值 (m/s)", fontsize=10)
                    ax5.set_title(
                        "各海拔高度平均风速差值", fontsize=12, fontweight="bold"
                    )
                    ax5.axhline(y=0, color="black", linestyle="--", linewidth=0.5)
                    ax5.grid(True, alpha=0.3)
                    ax5.legend(loc="best", fontsize=9)

            # 6. 统计摘要文本
            ax6 = plt.subplot(2, 3, 6)
            ax6.axis("off")

            # 创建统计摘要文本
            summary_text = "📊 统计摘要\n\n"
            summary_text += f"总数据点数: {basic_stats['total_points']:,}\n\n"

            summary_text += "风速差值分布:\n"
            summary_text += f"  • 正差值: {basic_stats['positive_count']:,} 点 ({basic_stats['positive_ratio']:.1f}%)\n"
            summary_text += f"  • 负差值: {basic_stats['negative_count']:,} 点 ({basic_stats['negative_ratio']:.1f}%)\n"
            summary_text += f"  • 接近零: {basic_stats['zero_count']:,} 点 ({basic_stats['zero_ratio']:.1f}%)\n\n"

            summary_text += "平均值统计:\n"
            summary_text += f"  • 总体平均差值: {mean_stats['mean_diff']:.3f} m/s\n"
            if mean_stats.get("mean_diff_bounded") != mean_stats["mean_diff"]:
                summary_text += (
                    f"  • 限定区域平均: {mean_stats['mean_diff_bounded']:.3f} m/s\n"
                )
            summary_text += f"  • 正差值平均: {mean_stats['mean_positive']:.3f} m/s\n"
            summary_text += f"  • 负差值平均: {mean_stats['mean_negative']:.3f} m/s\n\n"

            if "elevation" in statistics:
                elev_summary = statistics["elevation"]["summary"]
                summary_text += "地形统计:\n"
                summary_text += f"  • 海拔范围: {elev_summary['min_elevation']:.0f} - {elev_summary['max_elevation']:.0f} m\n"
                summary_text += f"  • 高度差: {elev_summary['elevation_range']:.0f} m\n"
                summary_text += f"  • 分析层数: {elev_summary['num_bins']}\n"

            ax6.text(
                0.1,
                0.9,
                summary_text,
                transform=ax6.transAxes,
                fontsize=10,
                verticalalignment="top",
                bbox=dict(
                    boxstyle="round,pad=0.5",
                    facecolor="white",
                    edgecolor="gray",
                    alpha=0.9,
                ),
            )

            # 添加总标题
            fig.suptitle(
                "WRF风速差值综合统计报告", fontsize=14, fontweight="bold", y=0.98
            )

            plt.tight_layout(rect=[0, 0, 1, 0.96])

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制统计报告时出错: {e}")
            raise

    def plot_wind_vectors(
        self, diff_data: Dict, save_path: Optional[str] = None, skip: int = 15
    ) -> str:
        """
        绘制风矢量差值图
        """
        try:
            logger.info("开始绘制风矢量图")

            fig, ax = self._setup_figure()

            # 稀疏化数据
            lon = diff_data["lon"][::skip, ::skip]
            lat = diff_data["lat"][::skip, ::skip]
            u_diff = diff_data["u_diff"][::skip, ::skip]
            v_diff = diff_data["v_diff"][::skip, ::skip]
            wspd_diff = diff_data["wspd_diff"]

            # 创建颜色映射
            levels, cmap, norm = self._create_diff_colormap(wspd_diff)

            # 绘制背景风速差值
            cf = ax.contourf(
                diff_data["lon"],
                diff_data["lat"],
                wspd_diff,
                levels=levels,
                cmap=cmap,
                norm=norm,
                alpha=0.6,
                extend="both",
            )

            # 计算风矢量颜色
            wind_speed_arrows = np.sqrt(u_diff**2 + v_diff**2)

            # 绘制风矢量
            Q = ax.quiver(
                lon,
                lat,
                u_diff,
                v_diff,
                wind_speed_arrows,
                scale=80,
                scale_units="inches",
                width=0.004,
                headwidth=3,
                headlength=4,
                cmap=cmap,
                norm=norm,
                alpha=0.9,
            )

            # 添加风矢量参考
            ax.quiverkey(
                Q,
                0.9,
                0.95,
                5,
                "5 m/s",
                coordinates="axes",
                fontproperties={"size": 10},
                labelcolor="black",
            )

            # 添加颜色条
            cbar = fig.colorbar(cf, ax=ax, orientation="vertical", pad=0.02, shrink=0.8)
            cbar.set_label(
                "风速差值 (m/s)",
                rotation=270,
                labelpad=20,
                fontsize=self.config.label_size,
            )
            cbar.ax.tick_params(labelsize=10)

            # 设置标签和标题
            ax.set_xlabel("经度 (°)", fontsize=self.config.label_size)
            ax.set_ylabel("纬度 (°)", fontsize=self.config.label_size)
            ax.set_title(
                "风矢量差值分布",
                fontsize=self.config.title_size,
                fontweight="bold",
                pad=20,
            )
            ax.set_aspect("equal", adjustable="box")

            # 添加网格
            if self.config.enable_grid:
                ax.grid(True, linestyle="--", alpha=0.3, linewidth=0.5)

            # 添加统计信息
            if self.config.enable_statistics and "stats" in diff_data:
                stats = diff_data["stats"]
                stats_text = f"最大风速差值: {stats['abs_max']:.1f} m/s"
                ax.text(
                    0.02,
                    0.02,
                    stats_text,
                    transform=ax.transAxes,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                    fontsize=10,
                )

            plt.tight_layout()

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制风矢量图时出错: {e}")
            raise


def main():
    """
    主函数 - WRF风速差值分析
    """
    try:
        # === 路径配置 ===
        wrf1_file = r"E:\wrf_out5\wrfout_d04_2022-07-01_18_00_00"
        wrf2_file = r"E:\wrf_out7\wrfout_d04_2022-07-01_18_00_00"

        # === 参数配置 ===
        time_idx = 0  # 时间索引
        level = 0  # 垂直层次

        # === 创建配置 ===
        plot_config = PlotConfig(
            figure_size=(12, 10),
            dpi=800,
            wind_speed_max=15.0,
            wind_speed_min=0.0,
            diff_levels=21,
            diff_symmetric=True,
            wind_colormap="RdYlBu_r",
            diff_colormap="RdBu_r",
            wind_arrow_step=10,
            enable_wind_arrows=True,
            enable_contour_lines=True,
            enable_statistics=True,
            enable_grid=True,
            enable_stations=True,  # 启用气象站点显示
            apply_bounds=True,  # 启用范围限制
            station_size=100.0,
            station_color="red",
            station_marker="^",
        )

        # === 创建提取器和绘图器 ===
        extractor = WRFDataExtractor()
        plotter = WindDifferencePlotter(plot_config)

        print("=" * 60)
        print("WRF风速差值分析")
        print("=" * 60)
        print(f"WRF文件1: {os.path.basename(wrf1_file)}")
        print(f"WRF文件2: {os.path.basename(wrf2_file)}")
        print(f"时间索引: {time_idx}")
        print(f"垂直层次: {level}")
        print("=" * 60)

        # === 读取WRF数据 ===
        print("\n1. 读取WRF数据...")
        wrf1_data = extractor.read_wrf_wind(wrf1_file, time_idx, level)
        if not wrf1_data:
            print("   ✗ 读取WRF1文件失败")
            return
        print(f"   ✓ 成功读取WRF1数据 (高度: {wrf1_data['height']})")
        print(f"     数据维度: {wrf1_data['wspd'].shape}")

        wrf2_data = extractor.read_wrf_wind(wrf2_file, time_idx, level)
        if not wrf2_data:
            print("   ✗ 读取WRF2文件失败")
            return
        print(f"   ✓ 成功读取WRF2数据 (高度: {wrf2_data['height']})")
        print(f"     数据维度: {wrf2_data['wspd'].shape}")

        # === 计算风速差值 ===
        print("\n2. 计算风速差值...")
        diff_data = extractor.calculate_wind_difference(wrf1_data, wrf2_data)
        print("   ✓ 差值计算完成")

        # === 输出统计信息 ===
        print("\n3. 风速差值统计:")
        stats = diff_data["stats"]
        print(f"   最大正差值: {stats['max']:.2f} m/s")
        print(f"   最大负差值: {stats['min']:.2f} m/s")
        print(f"   平均差值: {stats['mean']:.2f} m/s")
        print(f"   标准差: {stats['std']:.2f} m/s")

        # === 绘制图形 ===
        print("\n4. 绘制风速差值图...")
        try:
            output_file = plotter.plot_wind_difference(
                diff_data, wrf1_file, wrf2_file, save_path="wind_difference_styled.png"
            )
            if output_file:
                print(f"   ✓ 风速差值图已保存: {output_file}")
        except Exception as e:
            print(f"   ✗ 绘制差值图失败: {e}")

        print("\n5. 绘制对比图...")
        try:
            output_file = plotter.plot_comparison(
                wrf1_data,
                wrf2_data,
                diff_data,
                wrf1_file,
                wrf2_file,
                save_path="wind_comparison_styled.png",
            )
            if output_file:
                print(f"   ✓ 对比图已保存: {output_file}")
        except Exception as e:
            print(f"   ✗ 绘制对比图失败: {e}")

        print("\n6. 绘制风矢量差值图...")
        try:
            output_file = plotter.plot_wind_vectors(
                diff_data, save_path="wind_vectors_styled.png", skip=15
            )
            if output_file:
                print(f"   ✓ 风矢量图已保存: {output_file}")
        except Exception as e:
            print(f"   ✗ 绘制风矢量图失败: {e}")

        print("\n7. 绘制地形图...")
        try:
            # 绘制WRF1的地形图（带风场）
            output_file = plotter.plot_terrain(
                wrf1_data, save_path="terrain_wrf1_styled.png", show_wind=True
            )
            if output_file:
                print(f"   ✓ WRF1地形图已保存: {output_file}")

            # 也可以绘制纯地形图（不带风场）
            output_file = plotter.plot_terrain(
                wrf1_data, save_path="terrain_only_styled.png", show_wind=False
            )
            if output_file:
                print(f"   ✓ 纯地形图已保存: {output_file}")
        except Exception as e:
            print(f"   ✗ 绘制地形图失败: {e}")

        print("\n8. 计算综合统计信息...")
        try:
            # 计算综合统计
            comprehensive_stats = extractor.calculate_comprehensive_statistics(
                wrf1_data, wrf2_data, diff_data, plot_config
            )

            # 输出关键统计信息
            basic = comprehensive_stats["basic"]
            mean_vals = comprehensive_stats["mean_values"]

            print("   ✓ 综合统计计算完成")
            print("\n   风速差值正负占比:")
            print(f"     • 正差值: {basic['positive_ratio']:.1f}%")
            print(f"     • 负差值: {basic['negative_ratio']:.1f}%")
            print(f"     • 接近零: {basic['zero_ratio']:.1f}%")

            print("\n   平均差值统计:")
            print(f"     • 总体平均差值: {mean_vals['mean_diff']:.3f} m/s")
            print(f"     • 限定区域平均: {mean_vals['mean_diff_bounded']:.3f} m/s")
            print(f"     • 正差值平均: {mean_vals['mean_positive']:.3f} m/s")
            print(f"     • 负差值平均: {mean_vals['mean_negative']:.3f} m/s")

            if "elevation" in comprehensive_stats:
                elev_summary = comprehensive_stats["elevation"]["summary"]
                print("\n   地形相关统计:")
                print(
                    f"     • 海拔范围: {elev_summary['min_elevation']:.0f} - {elev_summary['max_elevation']:.0f} m"
                )
                print(f"     • 分析层数: {elev_summary['num_bins']}")

                # 输出各海拔段的关键信息
                for bin_stat in comprehensive_stats["elevation"]["bins"][
                    :3
                ]:  # 只显示前3个
                    print(
                        f"     • {bin_stat['elevation_range']}: "
                        f"平均差值={bin_stat['mean_diff']:.2f} m/s, "
                        f"正占比={bin_stat['positive_ratio']:.1f}%"
                    )

        except Exception as e:
            print(f"   ✗ 计算综合统计失败: {e}")
            comprehensive_stats = None

        print("\n9. 绘制统计报告图表...")
        try:
            if comprehensive_stats:
                output_file = plotter.plot_statistics_report(
                    comprehensive_stats, save_path="statistics_report_styled.png"
                )
                if output_file:
                    print(f"   ✓ 统计报告图表已保存: {output_file}")
        except Exception as e:
            print(f"   ✗ 绘制统计报告失败: {e}")

        print("\n" + "=" * 60)
        print("分析完成！")
        print("=" * 60)

    except Exception as e:
        logger.error(f"主程序执行出错: {e}")
        print(f"\n✗ 程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
