import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr
import sys
import os

# 全局静态配置变量
Station = 30
Dom_id = 4
SIMULATED_FILE_1 = os.path.join(
    r"E:\wrf_out5\csv", f"wrf_station_data_5min_parallel_d0{Dom_id}.csv"
)
SIMULATED_FILE_2 = os.path.join(
    r"E:\wrf_out7\csv", f"wrf_station_data_5min_parallel_d0{Dom_id}.csv"
)
OBSERVED_FILE = os.path.join(r"F:\项目\Code\csv", f"202207_{Station}M.csv")
SIMULATED_TIME_COLUMN = "local_time"
OBSERVED_TIME_COLUMN = "Time"
SIMULATED_WIND_COLUMN = 13  # 75M_75m_wind_speed_ms (正确的75m风速列)
OBSERVED_WIND_COLUMN = 4  # WS (m/s) 观测风速列索引
TIME_FORMAT = "%Y/%m/%d  %H:%M:%S"  # 时间格式：2022/7/1  8:00:00


def read_and_validate_data():
    """
    读取和验证两个模拟文件和一个观测文件
    """
    # 读取第一个模拟文件
    print(f"Reading simulated data 1 from: {SIMULATED_FILE_1}")
    sys.stdout.flush()
    try:
        sim_df_1 = pd.read_csv(SIMULATED_FILE_1)
        print(f"Simulated data 1 shape: {sim_df_1.shape}")
    except Exception as e:
        print(f"Error reading simulated file 1: {e}")
        sys.exit(1)

    # 读取第二个模拟文件
    print(f"Reading simulated data 2 from: {SIMULATED_FILE_2}")
    sys.stdout.flush()
    try:
        sim_df_2 = pd.read_csv(SIMULATED_FILE_2)
        print(f"Simulated data 2 shape: {sim_df_2.shape}")
    except Exception as e:
        print(f"Error reading simulated file 2: {e}")
        sys.exit(1)

    print(f"Reading observed data from: {OBSERVED_FILE}")
    sys.stdout.flush()
    try:
        # 检查文件扩展名决定读取方式
        if OBSERVED_FILE.endswith(".xlsx") or OBSERVED_FILE.endswith(".xls"):
            obs_df = pd.read_excel(OBSERVED_FILE)
        else:
            obs_df = pd.read_csv(OBSERVED_FILE)
        print(f"Observed data shape: {obs_df.shape}")
    except Exception as e:
        print(f"Error reading observed file: {e}")
        sys.exit(1)

    return sim_df_1, sim_df_2, obs_df


def align_data_by_time(sim_df_1, sim_df_2, obs_df):
    """
    通过时间列对齐两个模拟数据框和一个观测数据框
    """
    print("Aligning data by time...")
    sys.stdout.flush()

    # 转换时间列为datetime类型
    try:
        sim_df_1[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_1[SIMULATED_TIME_COLUMN], format=TIME_FORMAT
        )
        sim_df_2[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_2[SIMULATED_TIME_COLUMN], format=TIME_FORMAT
        )
        obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(
            obs_df[OBSERVED_TIME_COLUMN], format=TIME_FORMAT
        )
    except Exception as e:
        print(f"Error parsing time columns: {e}")
        # 尝试自动解析
        sim_df_1[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_1[SIMULATED_TIME_COLUMN]
        )
        sim_df_2[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_2[SIMULATED_TIME_COLUMN]
        )
        obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(obs_df[OBSERVED_TIME_COLUMN])

    # 先合并两个模拟数据
    sim_merged = pd.merge(
        sim_df_1,
        sim_df_2,
        on=SIMULATED_TIME_COLUMN,
        how="inner",
        suffixes=("_sim1", "_sim2"),
    )

    # 再与观测数据合并
    final_merged = pd.merge(
        sim_merged,
        obs_df,
        left_on=SIMULATED_TIME_COLUMN,
        right_on=OBSERVED_TIME_COLUMN,
        how="inner",
        suffixes=("", "_obs"),
    )

    print(f"Merged data shape: {final_merged.shape}")
    return final_merged


def align_data_by_index(sim_df, obs_df):
    """
    通过索引对齐两个数据框（当没有时间列时使用）
    """
    print("Aligning data by index...")
    sys.stdout.flush()

    # 确保两个数据框长度一致
    min_length = min(len(sim_df), len(obs_df))
    sim_df_aligned = sim_df.iloc[:min_length].copy()
    obs_df_aligned = obs_df.iloc[:min_length].copy()

    print(f"Aligned data length: {min_length}")
    return sim_df_aligned, obs_df_aligned


def extract_wind_data(merged_df):
    """
    从合并后的数据框中提取两个模拟和一个观测的风速数据
    """
    print("Extracting wind speed data...")
    sys.stdout.flush()

    try:
        print("Available columns after merge:")
        for i, col in enumerate(merged_df.columns):
            print(f"  {i}: {col}")

        # 查找数据列
        sim_wind_col_1 = None
        sim_wind_col_2 = None
        obs_wind_col = None

        # 查找第一个模拟数据的风速列
        for col in merged_df.columns:
            if f"{Station}M_{Station}m_wind_speed_ms_sim1" in col:
                sim_wind_col_1 = col
                break
            elif f"{Station}M_{Station}m_wind_speed_ms" in col and "_sim2" not in col:
                sim_wind_col_1 = col
                break

        # 查找第二个模拟数据的风速列
        for col in merged_df.columns:
            if f"{Station}M_{Station}m_wind_speed_ms_sim2" in col:
                sim_wind_col_2 = col
                break

        # 查找观测数据的风速列
        for col in merged_df.columns:
            if "Wind speed" in col:
                obs_wind_col = col
                break

        if sim_wind_col_1 is None or sim_wind_col_2 is None or obs_wind_col is None:
            print(f"Could not find wind speed columns:")
            print(f"  Simulated 1: {sim_wind_col_1}")
            print(f"  Simulated 2: {sim_wind_col_2}")
            print(f"  Observed: {obs_wind_col}")
            sys.exit(1)

        simulated_wind_speed_1 = merged_df[sim_wind_col_1]
        simulated_wind_speed_2 = merged_df[sim_wind_col_2]
        observed_wind_speed = merged_df[obs_wind_col]

        print(f"Using simulated wind column 1: {sim_wind_col_1}")
        print(f"Using simulated wind column 2: {sim_wind_col_2}")
        print(f"Using observed wind column: {obs_wind_col}")
        print(f"Simulated wind speed 1 shape: {simulated_wind_speed_1.shape}")
        print(f"Simulated wind speed 2 shape: {simulated_wind_speed_2.shape}")
        print(f"Observed wind speed shape: {observed_wind_speed.shape}")

        return simulated_wind_speed_1, simulated_wind_speed_2, observed_wind_speed

    except Exception as e:
        print(f"Error extracting wind speed data: {e}")
        sys.exit(1)


def plot_comparison(
    simulated_wind_speed_1,
    simulated_wind_speed_2,
    observed_wind_speed,
    merged_df,
    output_file=None,
):
    """
    绘制两个模拟和一个观测的风速对比图
    """
    print("Generating plot...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到当前目录
    if output_file is None:
        output_file = f"wind_speed_comparison_multi_{Station}m_d0{Dom_id}.png"

    # 获取时间数据用于横坐标
    time_data = merged_df[SIMULATED_TIME_COLUMN]

    plt.figure(figsize=(15, 8))
    plt.plot(
        time_data,
        simulated_wind_speed_1,
        label=f"YSU_SMAG",
        linewidth=1.2,
        alpha=0.8,
        color="blue",
    )
    plt.plot(
        time_data,
        simulated_wind_speed_2,
        label=f"YSU_SMAG_NBA",
        linewidth=1.2,
        alpha=0.8,
        color="green",
    )
    plt.plot(
        time_data,
        observed_wind_speed,
        label="Observed",
        linewidth=1.2,
        alpha=0.8,
        color="red",
    )

    plt.xlabel("Time")
    plt.ylabel("Wind Speed (m/s)")
    plt.title("Comparison of Multiple Simulated vs Observed Wind Speed")
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 设置时间格式显示
    import matplotlib.dates as mdates

    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
    plt.gca().xaxis.set_major_locator(
        mdates.HourLocator(interval=6)
    )  # 每6小时显示一个标签
    plt.xticks(rotation=0)

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches="tight")
    print(f"Plot saved to '{output_file}'")
    plt.close()


def calculate_metrics(
    simulated_wind_speed_1, simulated_wind_speed_2, observed_wind_speed
):
    """
    计算两个模拟数据与观测数据的统计指标
    """
    print("Calculating metrics...")
    sys.stdout.flush()

    def calc_single_metrics(sim_data, obs_data, name):
        # 转换为numpy数组并确保为数值类型
        sim_array = pd.to_numeric(sim_data, errors="coerce").values
        obs_array = pd.to_numeric(obs_data, errors="coerce").values

        # 移除NaN值
        mask = ~(np.isnan(sim_array) | np.isnan(obs_array))
        sim_clean = sim_array[mask]
        obs_clean = obs_array[mask]

        if len(sim_clean) == 0:
            print(f"Warning: No valid data points for {name} after removing NaN values")
            return None, None, None, None

        # 计算指标
        correlation, p_value = pearsonr(sim_clean, obs_clean)
        rmse = np.sqrt(mean_squared_error(obs_clean, sim_clean))
        mae = mean_absolute_error(obs_clean, sim_clean)
        mb = np.mean(sim_clean - obs_clean)

        return correlation, rmse, mae, mb

    # 计算两组指标
    metrics_1 = calc_single_metrics(
        simulated_wind_speed_1, observed_wind_speed, "Simulation 1"
    )
    metrics_2 = calc_single_metrics(
        simulated_wind_speed_2, observed_wind_speed, "Simulation 2"
    )

    return metrics_1, metrics_2


def save_results(metrics_1, metrics_2, output_file=None):
    """
    保存两组结果到文件
    """
    print("Saving results to file...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到当前目录
    if output_file is None:
        output_file = "wind_speed_statistics_multi.txt"

    with open(output_file, "w") as f:
        f.write("Multiple Wind Speed Analysis Results\n")
        f.write("=" * 40 + "\n\n")

        if metrics_1[0] is not None:
            f.write("Simulation 1 (WRF_OUT7) vs Observed:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Correlation Coefficient (R): {metrics_1[0]:.4f}\n")
            f.write(f"Root Mean Square Error (RMSE): {metrics_1[1]:.4f} m/s\n")
            f.write(f"Mean Absolute Error (MAE): {metrics_1[2]:.4f} m/s\n")
            f.write(f"Mean Bias (MB): {metrics_1[3]:.4f} m/s\n\n")

        if metrics_2[0] is not None:
            f.write("Simulation 2 (WRF_OUT8) vs Observed:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Correlation Coefficient (R): {metrics_2[0]:.4f}\n")
            f.write(f"Root Mean Square Error (RMSE): {metrics_2[1]:.4f} m/s\n")
            f.write(f"Mean Absolute Error (MAE): {metrics_2[2]:.4f} m/s\n")
            f.write(f"Mean Bias (MB): {metrics_2[3]:.4f} m/s\n\n")

        # 比较两个模拟的表现
        if metrics_1[0] is not None and metrics_2[0] is not None:
            f.write("Performance Comparison:\n")
            f.write("-" * 20 + "\n")
            better_r = "Simulation 1" if metrics_1[0] > metrics_2[0] else "Simulation 2"
            better_rmse = (
                "Simulation 1" if metrics_1[1] < metrics_2[1] else "Simulation 2"
            )
            better_mae = (
                "Simulation 1" if metrics_1[2] < metrics_2[2] else "Simulation 2"
            )
            f.write(f"Better Correlation: {better_r}\n")
            f.write(f"Lower RMSE: {better_rmse}\n")
            f.write(f"Lower MAE: {better_mae}\n")

    print(f"Results saved to '{output_file}'")


def main():
    """
    主函数
    """
    print("Starting multiple wind speed analysis...")
    print(f"Configuration:")
    print(f"  Simulated file 1: {SIMULATED_FILE_1}")
    print(f"  Simulated file 2: {SIMULATED_FILE_2}")
    print(f"  Observed file: {OBSERVED_FILE}")
    print(f"  Simulated time column: {SIMULATED_TIME_COLUMN}")
    print(f"  Observed time column: {OBSERVED_TIME_COLUMN}")
    print(f"  Time format: {TIME_FORMAT}")

    # 读取数据
    sim_df_1, sim_df_2, obs_df = read_and_validate_data()

    # 通过时间列对齐数据
    merged_df = align_data_by_time(sim_df_1, sim_df_2, obs_df)

    # 提取风速数据
    simulated_wind_speed_1, simulated_wind_speed_2, observed_wind_speed = (
        extract_wind_data(merged_df)
    )

    # 绘制图形
    plot_comparison(
        simulated_wind_speed_1, simulated_wind_speed_2, observed_wind_speed, merged_df
    )

    # 计算指标
    metrics_1, metrics_2 = calculate_metrics(
        simulated_wind_speed_1, simulated_wind_speed_2, observed_wind_speed
    )

    # 输出结果
    if metrics_1[0] is not None:
        print(f"\nSimulation 1 (WRF_OUT7) Results:")
        print(f"  Correlation Coefficient (R): {metrics_1[0]:.4f}")
        print(f"  Root Mean Square Error (RMSE): {metrics_1[1]:.4f} m/s")
        print(f"  Mean Absolute Error (MAE): {metrics_1[2]:.4f} m/s")
        print(f"  Mean Bias (MB): {metrics_1[3]:.4f} m/s")

    if metrics_2[0] is not None:
        print(f"\nSimulation 2 (WRF_OUT8) Results:")
        print(f"  Correlation Coefficient (R): {metrics_2[0]:.4f}")
        print(f"  Root Mean Square Error (RMSE): {metrics_2[1]:.4f} m/s")
        print(f"  Mean Absolute Error (MAE): {metrics_2[2]:.4f} m/s")
        print(f"  Mean Bias (MB): {metrics_2[3]:.4f} m/s")

    # 保存结果
    save_results(metrics_1, metrics_2)

    print(
        f"\nMultiple simulation analysis completed. Results saved to current directory."
    )


if __name__ == "__main__":
    main()
