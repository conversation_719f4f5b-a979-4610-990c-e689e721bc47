#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux环境配置文件
将此文件放在与analyze_wind_data_separated.py相同目录
"""

import os
import platform

# 检测操作系统
IS_WINDOWS = platform.system() == 'Windows'
IS_LINUX = platform.system() == 'Linux'

if IS_LINUX:
    # Linux路径配置 - 请根据实际情况修改
    WRF_OUT_BASE = "/home/<USER>/wrf_out"  # WRF输出基础路径
    OBS_DATA_PATH = "/home/<USER>/project/Code/csv"  # 观测数据路径
    OUTPUT_PATH = "/home/<USER>/analysis_results"  # 输出路径

    # 创建输出目录
    os.makedirs(OUTPUT_PATH, exist_ok=True)

elif IS_WINDOWS:
    # Windows路径配置
    WRF_OUT_BASE = r"E:\wrf_out"
    OBS_DATA_PATH = r"F:\项目\Code\csv"
    OUTPUT_PATH = r"F:\项目"

else:
    print(f"未识别的操作系统: {platform.system()}")
    exit(1)

# 共同配置
DOM_ID = 4  # 嵌套域ID
WRF_SUFFIXES = [str(i) for i in range(2, 15)]  # wrf_out2 到 wrf_out14
STATION_TYPES = ["30M", "75M"]  # 站点类型

print(f"运行环境: {platform.system()}")
print(f"WRF路径: {WRF_OUT_BASE}")
print(f"观测数据: {OBS_DATA_PATH}")
print(f"输出路径: {OUTPUT_PATH}")