import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr
import sys
import os
import matplotlib.dates as mdates
import matplotlib.font_manager as fm
import matplotlib as mpl

# 设置中文和英文字体
# 使用黑体实现中文加粗效果，Times New Roman Bold实现英文加粗
import matplotlib as mpl

mpl.rcParams["font.sans-serif"] = [
    "SimSun",
    "Times New Roman",
]  # 微软雅黑(天然粗体)和Arial Black
mpl.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题
mpl.rcParams["font.weight"] = "bold"  # 设置全局字体加粗
mpl.rcParams["axes.labelweight"] = "bold"  # 轴标签加粗
mpl.rcParams["axes.titleweight"] = "bold"  # 标题加粗
# mpl.rcParams["xtick.labelsize"] = FONT_SIZES["tick_label"] if 'FONT_SIZES' in locals() else 20
# mpl.rcParams["ytick.labelsize"] = FONT_SIZES["tick_label"] if 'FONT_SIZES' in locals() else 20

# 全局静态配置变量
Station = 75  # 站点高度（30或75米）
Dom_id = 4  # 嵌套域ID
SIMULATED_FILE_1 = os.path.join(
    r"E:\wrf_out10\csv", f"wrf_station_data_5min_parallel_d0{Dom_id}.csv"
)
SIMULATED_FILE_2 = os.path.join(
    r"E:\wrf_out7\csv", f"wrf_station_data_5min_parallel_d0{Dom_id}.csv"
)
OBSERVED_FILE = os.path.join(r"F:\项目\Code\csv", f"202207_{Station}M.csv")
SIMULATED_TIME_COLUMN = "local_time"
OBSERVED_TIME_COLUMN = "Time"
TIME_FORMAT = "%Y/%m/%d  %H:%M:%S"  # 时间格式：2022/7/1  8:00:00

# 配置方案名称
SCHEME_NAMES = {"observed": "Observed", "sim1": "YSU_SMAG", "sim2": "YSU_SMAG_NBA"}

# 配置颜色方案
COLOR_SCHEME = {"observed": "red", "sim1": "blue", "sim2": "green"}

# 字体大小配置
FONT_SIZES = {
    "title": 24,  # 标题字体大小
    "axis_label": 18,  # 轴标签字体大小
    "tick_label": 20,  # 刻度标签字体大小
    "legend": 20,  # 图例字体大小
    "reference": 20,  # 参考标注字体大小
    "legend_marker": 12,  # 图例标记大小
}


def read_and_validate_data():
    """
    读取和验证两个模拟文件和一个观测文件
    """
    # 读取第一个模拟文件
    print(f"Reading simulated data 1 from: {SIMULATED_FILE_1}")
    sys.stdout.flush()
    try:
        sim_df_1 = pd.read_csv(SIMULATED_FILE_1)
        print(f"Simulated data 1 shape: {sim_df_1.shape}")
    except Exception as e:
        print(f"Error reading simulated file 1: {e}")
        sys.exit(1)

    # 读取第二个模拟文件
    print(f"Reading simulated data 2 from: {SIMULATED_FILE_2}")
    sys.stdout.flush()
    try:
        sim_df_2 = pd.read_csv(SIMULATED_FILE_2)
        print(f"Simulated data 2 shape: {sim_df_2.shape}")
    except Exception as e:
        print(f"Error reading simulated file 2: {e}")
        sys.exit(1)

    print(f"Reading observed data from: {OBSERVED_FILE}")
    sys.stdout.flush()
    try:
        # 检查文件扩展名决定读取方式
        if OBSERVED_FILE.endswith(".xlsx") or OBSERVED_FILE.endswith(".xls"):
            obs_df = pd.read_excel(OBSERVED_FILE)
        else:
            obs_df = pd.read_csv(OBSERVED_FILE)
        print(f"Observed data shape: {obs_df.shape}")
    except Exception as e:
        print(f"Error reading observed file: {e}")
        sys.exit(1)

    return sim_df_1, sim_df_2, obs_df


def align_data_by_time(sim_df_1, sim_df_2, obs_df):
    """
    通过时间列对齐两个模拟数据框和一个观测数据框
    根据模拟数据的时间范围自动确定数据范围
    """
    print("Aligning data by time...")
    sys.stdout.flush()

    # 转换时间列为datetime类型
    try:
        sim_df_1[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_1[SIMULATED_TIME_COLUMN], format=TIME_FORMAT
        )
        sim_df_2[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_2[SIMULATED_TIME_COLUMN], format=TIME_FORMAT
        )
        obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(
            obs_df[OBSERVED_TIME_COLUMN], format=TIME_FORMAT
        )
    except Exception as e:
        print(f"Error parsing time columns: {e}")
        # 尝试自动解析
        sim_df_1[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_1[SIMULATED_TIME_COLUMN]
        )
        sim_df_2[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df_2[SIMULATED_TIME_COLUMN]
        )
        obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(obs_df[OBSERVED_TIME_COLUMN])

    # 获取模拟数据的时间范围
    sim1_min_time = sim_df_1[SIMULATED_TIME_COLUMN].min()
    sim1_max_time = sim_df_1[SIMULATED_TIME_COLUMN].max()
    sim2_min_time = sim_df_2[SIMULATED_TIME_COLUMN].min()
    sim2_max_time = sim_df_2[SIMULATED_TIME_COLUMN].max()

    # 使用两个模拟数据的共同时间范围
    time_start = max(sim1_min_time, sim2_min_time)
    time_end = min(sim1_max_time, sim2_max_time)

    print(f"Simulation data time range:")
    print(f"  Sim1: {sim1_min_time} to {sim1_max_time}")
    print(f"  Sim2: {sim2_min_time} to {sim2_max_time}")
    print(f"  Using common range: {time_start} to {time_end}")

    # 根据模拟数据的时间范围过滤所有数据
    sim_df_1 = sim_df_1[
        (sim_df_1[SIMULATED_TIME_COLUMN] >= time_start)
        & (sim_df_1[SIMULATED_TIME_COLUMN] <= time_end)
    ]
    sim_df_2 = sim_df_2[
        (sim_df_2[SIMULATED_TIME_COLUMN] >= time_start)
        & (sim_df_2[SIMULATED_TIME_COLUMN] <= time_end)
    ]
    obs_df = obs_df[
        (obs_df[OBSERVED_TIME_COLUMN] >= time_start)
        & (obs_df[OBSERVED_TIME_COLUMN] <= time_end)
    ]

    print(f"Data after filtering:")
    print(f"  Sim1 shape: {sim_df_1.shape}")
    print(f"  Sim2 shape: {sim_df_2.shape}")
    print(f"  Obs shape: {obs_df.shape}")

    # 先合并两个模拟数据
    sim_merged = pd.merge(
        sim_df_1,
        sim_df_2,
        on=SIMULATED_TIME_COLUMN,
        how="inner",
        suffixes=("_sim1", "_sim2"),
    )

    # 再与观测数据合并
    final_merged = pd.merge(
        sim_merged,
        obs_df,
        left_on=SIMULATED_TIME_COLUMN,
        right_on=OBSERVED_TIME_COLUMN,
        how="inner",
        suffixes=("", "_obs"),
    )

    print(f"Merged data shape: {final_merged.shape}")
    return final_merged


def extract_wind_data(merged_df):
    """
    从合并后的数据框中提取风速和风向数据
    直接读取风向数据，不通过风速计算
    """
    print("Extracting wind speed and direction data...")
    sys.stdout.flush()

    try:
        print("Available columns after merge:")
        for i, col in enumerate(merged_df.columns):
            print(f"  {i}: {col}")

        # 初始化返回值字典
        wind_data = {
            "observed": {"speed": None, "direction": None},
            "sim1": {"speed": None, "direction": None},
            "sim2": {"speed": None, "direction": None},
        }

        # 查找风速和风向列
        # 查找第一个模拟数据的风速和风向列
        for col in merged_df.columns:
            # 风速列
            if f"{Station}M_{Station}m_wind_speed_ms_sim1" in col:
                wind_data["sim1"]["speed"] = merged_df[col]
            elif (
                f"{Station}M_{Station}m_wind_speed_ms" in col
                and "_sim2" not in col
                and "_obs" not in col
            ):
                wind_data["sim1"]["speed"] = merged_df[col]

            # 风向列 - 直接读取风向度数
            if f"{Station}M_{Station}m_wind_dir_deg_sim1" in col:
                wind_data["sim1"]["direction"] = merged_df[col]
            elif (
                f"{Station}M_{Station}m_wind_dir_deg" in col
                and "_sim2" not in col
                and "_obs" not in col
            ):
                wind_data["sim1"]["direction"] = merged_df[col]

        # 查找第二个模拟数据的风速和风向列
        for col in merged_df.columns:
            # 风速列
            if f"{Station}M_{Station}m_wind_speed_ms_sim2" in col:
                wind_data["sim2"]["speed"] = merged_df[col]
            # 风向列 - 直接读取风向度数
            if f"{Station}M_{Station}m_wind_dir_deg_sim2" in col:
                wind_data["sim2"]["direction"] = merged_df[col]

        # 查找观测数据的风速和风向列
        for col in merged_df.columns:
            # 风速列
            if "Wind speed" in col or "WS" in col:
                wind_data["observed"]["speed"] = merged_df[col]
            # 风向列 - 直接读取风向度数
            if "Wind_Dir" in col or "Wind direction" in col or "WD" in col:
                wind_data["observed"]["direction"] = merged_df[col]

        # 验证数据完整性并显示样本数据
        for scheme in ["observed", "sim1", "sim2"]:
            if wind_data[scheme]["speed"] is None:
                print(f"Warning: Could not find wind speed for {scheme}")
            else:
                print(
                    f"{SCHEME_NAMES[scheme]} wind speed shape: {wind_data[scheme]['speed'].shape}"
                )
                print(
                    f"  Sample wind speed: {wind_data[scheme]['speed'].iloc[:5].values}"
                )

            if wind_data[scheme]["direction"] is None:
                print(f"Warning: Could not find wind direction for {scheme}")
            else:
                print(
                    f"{SCHEME_NAMES[scheme]} wind direction shape: {wind_data[scheme]['direction'].shape}"
                )
                print(
                    f"  Sample wind direction (degrees): {wind_data[scheme]['direction'].iloc[:5].values}"
                )

        return wind_data

    except Exception as e:
        print(f"Error extracting wind data: {e}")
        sys.exit(1)


def plot_triple_wind_vectors(wind_data, merged_df, output_file=None):
    """
    绘制三种方案的风矢量对比图
    实测数据在最上方，两个模拟方案在中间和下方
    使用analyze_wind_data_separated.py的风矢量绘图风格
    30分钟间隔显示
    """
    print("Generating triple wind vector comparison plot...")
    print("Using direct wind direction data from stations (in degrees)")
    print("Display interval: 60 minutes (1 hour)")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到当前目录
    if output_file is None:
        output_file = f"wind_vector_triple_comparison_{Station}m_d0{Dom_id}.png"

    # 获取时间数据用于横坐标
    time_data = merged_df[SIMULATED_TIME_COLUMN]

    # 创建图形（减小宽度以去除右侧空白）
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    fig.patch.set_facecolor("white")

    # 设置Y轴位置（实测在最上方）
    y_positions = {
        "observed": 2.5,  # 最上方
        "sim1": 1.5,  # 中间
        "sim2": 0.5,  # 最下方
    }

    # 颜色方案
    color_scheme = {
        "observed": "red",  # 红色
        "sim1": "blue",  # 蓝色
        "sim2": "green",  # 绿色
    }

    # 每60分钟(1小时)显示一个箭头
    # 根据实际数据间隔调整
    time_diff = (
        time_data.iloc[1] - time_data.iloc[0]
    ).total_seconds() / 60  # 转换为分钟
    if time_diff <= 5:  # 5分钟数据
        step = 12  # 每12个点 = 60分钟
    elif time_diff <= 10:  # 10分钟数据
        step = 6  # 每6个点 = 60分钟
    elif time_diff <= 15:  # 15分钟数据
        step = 4  # 每4个点 = 60分钟
    elif time_diff <= 30:  # 30分钟数据
        step = 2  # 每2个点 = 60分钟
    else:
        step = 1  # 数据间隔已经>=60分钟

    print(
        f"Data interval: {time_diff:.1f} minutes, using step={step} for 60-min (1h) display"
    )

    time_subset = time_data[::step]
    time_numeric = mdates.date2num(time_subset)

    # 绘制每种方案的风矢量
    for scheme in ["observed", "sim1", "sim2"]:
        if wind_data[scheme]["speed"] is None or wind_data[scheme]["direction"] is None:
            print(f"Skipping {SCHEME_NAMES[scheme]} due to missing data")
            continue

        # 转换为数值类型并清理数据
        speed = pd.to_numeric(wind_data[scheme]["speed"], errors="coerce")
        direction = pd.to_numeric(wind_data[scheme]["direction"], errors="coerce")

        # 取子集
        speed_subset = speed[::step]
        direction_subset = direction[::step]

        # 输出调试信息
        valid_speed = ~np.isnan(speed_subset)
        valid_dir = ~np.isnan(direction_subset)
        print(f"{SCHEME_NAMES[scheme]}:")
        print(f"  Valid speed points: {np.sum(valid_speed)}/{len(speed_subset)}")
        print(f"  Valid direction points: {np.sum(valid_dir)}/{len(direction_subset)}")
        if np.sum(valid_dir) > 0:
            print(
                f"  Direction range: {np.nanmin(direction_subset):.1f}° to {np.nanmax(direction_subset):.1f}°"
            )

        # 转换风向为弧度（气象风向转数学风向）
        # 气象风向：0°=北，90°=东，180°=南，270°=西
        # 数学角度：0°=东，90°=北，180°=西，270°=南
        dir_rad = np.radians(90 - direction_subset)

        # 计算风矢量分量
        u = speed_subset * np.cos(dir_rad)
        v = speed_subset * np.sin(dir_rad)

        # 创建Y坐标
        y = np.ones(len(time_numeric)) * y_positions[scheme]

        # 绘制风矢量
        valid = ~(np.isnan(u) | np.isnan(v))
        valid_count = np.sum(valid)

        if np.any(valid):
            print(f"  Plotting {valid_count} valid wind vectors")

            q = ax.quiver(
                time_numeric[valid],
                y[valid],
                u[valid],
                v[valid],
                scale=100,
                scale_units="width",
                width=0.003,
                color=color_scheme[scheme],
                alpha=0.8,
                label=SCHEME_NAMES[scheme],
            )

    # 添加参考箭头显示比例尺（调整位置以适应缩小的图形）
    ref_speed = 5  # 5 m/s 参考风速
    ref_x = time_numeric[-1] + 0.02 * (time_numeric[-1] - time_numeric[0])  # 相对位置
    ref_y = 3.2
    """ax.quiver(
        ref_x,
        ref_y,
        ref_speed,
        0,
        scale=100,
        scale_units="width",
        width=0.004,
        color="black",
        alpha=0.9,
    )
    ax.text(
        ref_x,
        ref_y + 0.15,
        f"{ref_speed} m/s",
        ha="center",
        va="bottom",
        fontsize=FONT_SIZES["reference"],
        fontweight='bold'
    )"""

    # 设置图形属性
    # ax.set_ylabel("Wind Vectors", fontsize=FONT_SIZES["axis_label"], fontweight='bold')
    ax.set_xlabel("Time", fontsize=FONT_SIZES["axis_label"], fontweight="bold")
    ax.set_title(
        f"风矢量对比图",
        fontsize=FONT_SIZES["title"],
        fontweight="bold",
    )
    # 设置X轴范围，紧贴数据边界以去除空白
    ax.set_xlim(
        time_numeric[0] - 0.01 * (time_numeric[-1] - time_numeric[0]),
        time_numeric[-1] + 0.01 * (time_numeric[-1] - time_numeric[0]),
    )
    ax.set_ylim(0, 3.5)
    ax.grid(True, alpha=0.3)

    # 设置Y轴标签
    ax.set_yticks([0.5, 1.5, 2.5])
    ax.set_yticklabels(
        [SCHEME_NAMES["sim2"], SCHEME_NAMES["sim1"], SCHEME_NAMES["observed"]],
        rotation=90,
        va="center",
        fontsize=FONT_SIZES["tick_label"],
        fontweight="bold",
    )  # 垂直方向，使用全局字体大小，加粗

    # 设置时间格式显示
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%m-%d-%H"))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=8))  # 每8小时显示主刻度
    ax.xaxis.set_minor_locator(mdates.HourLocator(interval=4))  # 每4小时显示次刻度
    plt.xticks(
        rotation=0, fontsize=FONT_SIZES["tick_label"], fontweight="bold"
    )  # 水平方向，使用全局字体大小，加粗

    # 添加图例
    # 创建自定义图例元素
    legend_elements = [
        plt.Line2D(
            [0],
            [0],
            marker=">",
            markersize=FONT_SIZES["legend_marker"],
            color="red",
            linestyle="None",
            label="Observed",
        ),
        plt.Line2D(
            [0],
            [0],
            marker=">",
            markersize=FONT_SIZES["legend_marker"],
            color="blue",
            linestyle="None",
            label="YSU_SMAG",
        ),
        plt.Line2D(
            [0],
            [0],
            marker=">",
            markersize=FONT_SIZES["legend_marker"],
            color="green",
            linestyle="None",
            label="YSU_SMAG_NBA",
        ),
    ]
    ax.legend(
        handles=legend_elements,
        loc="upper left",
        framealpha=0.9,
        fontsize=FONT_SIZES["legend"],
        prop={"weight": "bold", "size": FONT_SIZES["legend"]},
    )

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches="tight")
    print(f"Triple wind vector plot saved to '{output_file}'")
    plt.show()


def add_statistics_box(ax, wind_data, time_numeric):
    """
    在图中添加统计信息框
    """
    stats_text = "Wind Speed Statistics (m/s):\n"
    stats_text += "─" * 25 + "\n"

    for scheme in ["observed", "sim1", "sim2"]:
        if wind_data[scheme]["speed"] is not None:
            speed = pd.to_numeric(wind_data[scheme]["speed"], errors="coerce")
            speed_clean = speed[~np.isnan(speed)]
            if len(speed_clean) > 0:
                mean_speed = np.mean(speed_clean)
                max_speed = np.max(speed_clean)
                std_speed = np.std(speed_clean)
                stats_text += f"{SCHEME_NAMES[scheme]:12s}: "
                stats_text += (
                    f"μ={mean_speed:.1f}, max={max_speed:.1f}, σ={std_speed:.1f}\n"
                )

    # 添加文本框
    props = dict(boxstyle="round", facecolor="wheat", alpha=0.8)
    ax.text(
        time_numeric[0],
        3.2,
        stats_text,
        fontsize=9,
        verticalalignment="top",
        bbox=props,
    )


def calculate_metrics_triple(wind_data):
    """
    计算三种方案之间的统计指标
    """
    print("\nCalculating comparison metrics...")
    sys.stdout.flush()

    results = {}

    # 计算模拟方案与观测的对比
    if wind_data["observed"]["speed"] is not None:
        obs_speed = pd.to_numeric(
            wind_data["observed"]["speed"], errors="coerce"
        ).values
        obs_dir = (
            pd.to_numeric(wind_data["observed"]["direction"], errors="coerce").values
            if wind_data["observed"]["direction"] is not None
            else None
        )

        for scheme in ["sim1", "sim2"]:
            if wind_data[scheme]["speed"] is not None:
                sim_speed = pd.to_numeric(
                    wind_data[scheme]["speed"], errors="coerce"
                ).values
                sim_dir = (
                    pd.to_numeric(
                        wind_data[scheme]["direction"], errors="coerce"
                    ).values
                    if wind_data[scheme]["direction"] is not None
                    else None
                )

                # 移除NaN值
                mask = ~(np.isnan(sim_speed) | np.isnan(obs_speed))
                sim_clean = sim_speed[mask]
                obs_clean = obs_speed[mask]

                if len(sim_clean) > 0:
                    # 计算风速指标
                    correlation, _ = pearsonr(sim_clean, obs_clean)
                    rmse = np.sqrt(mean_squared_error(obs_clean, sim_clean))
                    mae = mean_absolute_error(obs_clean, sim_clean)
                    mb = np.mean(sim_clean - obs_clean)

                    results[scheme] = {
                        "speed_correlation": correlation,
                        "speed_rmse": rmse,
                        "speed_mae": mae,
                        "speed_mb": mb,
                    }

                    # 如果有风向数据，计算风向指标
                    if sim_dir is not None and obs_dir is not None:
                        dir_mask = ~(np.isnan(sim_dir) | np.isnan(obs_dir))
                        sim_dir_clean = sim_dir[dir_mask]
                        obs_dir_clean = obs_dir[dir_mask]

                        if len(sim_dir_clean) > 0:
                            # 计算风向差（考虑环形特性）
                            dir_diff = calculate_wind_direction_difference(
                                sim_dir_clean, obs_dir_clean
                            )
                            dir_mae = np.mean(np.abs(dir_diff))
                            dir_mb = calculate_mean_wind_direction_bias(dir_diff)
                            dir_rmse = np.sqrt(np.mean(dir_diff**2))

                            results[scheme]["dir_mae"] = dir_mae
                            results[scheme]["dir_mb"] = dir_mb
                            results[scheme]["dir_rmse"] = dir_rmse

    return results


def calculate_wind_direction_difference(sim_dir, obs_dir):
    """
    计算风向差异，考虑环形特性（0-360度）
    返回范围在-180到180之间的角度差
    """
    diff = sim_dir - obs_dir

    # 将差异调整到-180到180度范围
    diff = np.where(diff > 180, diff - 360, diff)
    diff = np.where(diff < -180, diff + 360, diff)

    return diff


def calculate_mean_wind_direction_bias(dir_diff):
    """
    计算平均风向偏差，使用矢量平均法
    """
    # 转换为弧度
    dir_diff_rad = np.radians(dir_diff)

    # 计算矢量分量
    mean_sin = np.mean(np.sin(dir_diff_rad))
    mean_cos = np.mean(np.cos(dir_diff_rad))

    # 计算平均角度
    mean_angle = np.degrees(np.arctan2(mean_sin, mean_cos))

    return mean_angle


def save_results_triple(metrics, output_file=None):
    """
    保存三种方案对比结果到文件
    """
    print("Saving results to file...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到当前目录
    if output_file is None:
        output_file = f"wind_vector_triple_statistics_{Station}m_d0{Dom_id}.txt"

    with open(output_file, "w") as f:
        f.write(f"Triple Wind Vector Analysis Results - {Station}m Height\n")
        f.write("=" * 60 + "\n\n")

        for scheme in ["sim1", "sim2"]:
            if scheme in metrics:
                f.write(f"{SCHEME_NAMES[scheme]} vs {SCHEME_NAMES['observed']}:\n")
                f.write("-" * 40 + "\n")

                # 风速统计
                f.write("Wind Speed Statistics:\n")
                f.write(
                    f"  Correlation Coefficient (R): {metrics[scheme]['speed_correlation']:.4f}\n"
                )
                f.write(
                    f"  Root Mean Square Error (RMSE): {metrics[scheme]['speed_rmse']:.4f} m/s\n"
                )
                f.write(
                    f"  Mean Absolute Error (MAE): {metrics[scheme]['speed_mae']:.4f} m/s\n"
                )
                f.write(f"  Mean Bias (MB): {metrics[scheme]['speed_mb']:.4f} m/s\n")

                # 风向统计（如果存在）
                if "dir_mae" in metrics[scheme]:
                    f.write("\nWind Direction Statistics:\n")
                    f.write(
                        f"  Mean Absolute Error (MAE): {metrics[scheme]['dir_mae']:.2f}°\n"
                    )
                    f.write(f"  Mean Bias (MB): {metrics[scheme]['dir_mb']:.2f}°\n")
                    f.write(
                        f"  Root Mean Square Error (RMSE): {metrics[scheme]['dir_rmse']:.2f}°\n"
                    )

                f.write("\n")

        # 比较两个模拟方案
        if "sim1" in metrics and "sim2" in metrics:
            f.write("Performance Comparison:\n")
            f.write("-" * 40 + "\n")

            better_r = (
                SCHEME_NAMES["sim1"]
                if metrics["sim1"]["speed_correlation"]
                > metrics["sim2"]["speed_correlation"]
                else SCHEME_NAMES["sim2"]
            )
            better_rmse = (
                SCHEME_NAMES["sim1"]
                if metrics["sim1"]["speed_rmse"] < metrics["sim2"]["speed_rmse"]
                else SCHEME_NAMES["sim2"]
            )
            better_mae = (
                SCHEME_NAMES["sim1"]
                if metrics["sim1"]["speed_mae"] < metrics["sim2"]["speed_mae"]
                else SCHEME_NAMES["sim2"]
            )

            f.write(f"Better Correlation: {better_r}\n")
            f.write(f"Lower RMSE: {better_rmse}\n")
            f.write(f"Lower MAE: {better_mae}\n")

    print(f"Results saved to '{output_file}'")


def plot_wind_speed_comparison(wind_data, merged_df, output_file=None):
    """
    绘制三种方案的风速时间序列对比图
    美化版本
    """
    print("Generating wind speed comparison plot...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到当前目录
    if output_file is None:
        output_file = f"wind_speed_triple_comparison_{Station}m_d0{Dom_id}.png"

    # 获取时间数据用于横坐标
    time_data = merged_df[SIMULATED_TIME_COLUMN]

    # 创建美化的图形
    plt.style.use("seaborn-v0_8-whitegrid")
    fig, ax = plt.subplots(figsize=(16, 8))
    fig.patch.set_facecolor("white")

    # 更优雅的颜色方案
    color_scheme = {
        "observed": "#DC143C",  # 深红色
        "sim1": "#1E90FF",  # 道奇蓝
        "sim2": "#32CD32",  # 酸橙绿
    }

    # 线条样式
    line_styles = {
        "observed": "-",  # 实线
        "sim1": "--",  # 虚线
        "sim2": "-.",  # 点划线
    }

    # 绘制三条线
    for scheme in ["observed", "sim1", "sim2"]:
        if wind_data[scheme]["speed"] is not None:
            ax.plot(
                time_data,
                wind_data[scheme]["speed"],
                label=SCHEME_NAMES[scheme],
                linewidth=2.0,
                alpha=0.85,
                color=color_scheme[scheme],
                linestyle=line_styles[scheme],
            )

    # 设置图形属性
    ax.set_xlabel("Time", fontsize=14, fontweight="bold")
    ax.set_ylabel("Wind Speed (m/s)", fontsize=14, fontweight="bold")
    ax.set_title(
        f"Wind Speed Comparison at {Station}m Height (Domain {Dom_id})",
        fontsize=16,
        fontweight="bold",
        pad=20,
    )

    # 优化图例
    ax.legend(
        loc="upper left",
        fontsize=12,
        frameon=True,
        fancybox=True,
        shadow=True,
        framealpha=0.95,
    )

    # 网格样式
    ax.grid(True, alpha=0.3, linestyle=":", linewidth=0.8, color="gray")
    ax.set_axisbelow(True)

    # 设置时间格式显示
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%m/%d %H:00"))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))

    # X轴刻度样式
    plt.xticks(rotation=30, ha="right")
    ax.tick_params(axis="both", labelsize=11)
    ax.tick_params(axis="x", which="minor", length=3)
    ax.tick_params(axis="x", which="major", length=6)

    # 设置Y轴范围
    y_min = 0
    y_max = (
        max(
            [
                wind_data[s]["speed"].max()
                for s in ["observed", "sim1", "sim2"]
                if wind_data[s]["speed"] is not None
            ]
        )
        * 1.1
    )
    ax.set_ylim(y_min, y_max)

    # 边框样式
    for spine in ["top", "right"]:
        ax.spines[spine].set_visible(False)
    for spine in ["left", "bottom"]:
        ax.spines[spine].set_linewidth(1)
        ax.spines[spine].set_edgecolor("#666666")

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches="tight", facecolor="white")
    print(f"Wind speed plot saved to '{output_file}'")
    plt.show()


def main():
    """
    主函数 - 仅生成风矢量对比图
    """
    print("=" * 60)
    print(f"Starting Triple Wind Vector Analysis")
    print(f"Station Height: {Station}m")
    print(f"Domain: d0{Dom_id}")
    print("=" * 60)
    print(f"Configuration:")
    print(f"  Observed file: {OBSERVED_FILE}")
    print(f"  Simulated file 1 (YSU_SMAG): {SIMULATED_FILE_1}")
    print(f"  Simulated file 2 (YSU_SMAG_NBA): {SIMULATED_FILE_2}")
    print(f"  Time format: {TIME_FORMAT}")
    print()

    # 读取数据
    sim_df_1, sim_df_2, obs_df = read_and_validate_data()

    # 通过时间列对齐数据 - 根据模拟数据的时间范围
    merged_df = align_data_by_time(sim_df_1, sim_df_2, obs_df)

    # 提取风速和风向数据
    wind_data = extract_wind_data(merged_df)

    # 绘制三重风矢量对比图
    plot_triple_wind_vectors(wind_data, merged_df)

    # 计算统计指标
    metrics = calculate_metrics_triple(wind_data)

    # 输出结果
    print("\n" + "=" * 60)
    print("ANALYSIS RESULTS:")
    print("=" * 60)

    for scheme in ["sim1", "sim2"]:
        if scheme in metrics:
            print(f"\n{SCHEME_NAMES[scheme]} vs {SCHEME_NAMES['observed']}:")
            print(f"  Wind Speed:")
            print(
                f"    Correlation Coefficient (R): {metrics[scheme]['speed_correlation']:.4f}"
            )
            print(
                f"    Root Mean Square Error (RMSE): {metrics[scheme]['speed_rmse']:.4f} m/s"
            )
            print(
                f"    Mean Absolute Error (MAE): {metrics[scheme]['speed_mae']:.4f} m/s"
            )
            print(f"    Mean Bias (MB): {metrics[scheme]['speed_mb']:.4f} m/s")

            if "dir_mae" in metrics[scheme]:
                print(f"  Wind Direction:")
                print(
                    f"    Mean Absolute Error (MAE): {metrics[scheme]['dir_mae']:.2f}°"
                )
                print(f"    Mean Bias (MB): {metrics[scheme]['dir_mb']:.2f}°")
                print(
                    f"    Root Mean Square Error (RMSE): {metrics[scheme]['dir_rmse']:.2f}°"
                )

    # 保存结果
    save_results_triple(metrics)

    print(f"\n{'='*60}")
    print("Triple wind vector analysis completed successfully!")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
