import numpy as np

def calculate_wind_direction_class_accuracy_original(sim_dir, obs_dir, n_classes=16):
    """原始版本"""
    class_width = 360 / n_classes
    sim_classes = np.floor((sim_dir % 360) / class_width).astype(int)
    obs_classes = np.floor((obs_dir % 360) / class_width).astype(int)

    correct = np.sum(sim_classes == obs_classes)
    total = len(sim_classes)
    class_accuracy = (correct / total) * 100 if total > 0 else 0
    return class_accuracy, sim_classes, obs_classes


def calculate_wind_direction_class_accuracy_fixed(sim_dir, obs_dir, n_classes=16):
    """修复版本"""
    class_width = 360 / n_classes
    half_width = class_width / 2

    # 将风向调整半个分类宽度，使北向居中
    adjusted_sim = (sim_dir + half_width) % 360
    adjusted_obs = (obs_dir + half_width) % 360

    sim_classes = np.floor(adjusted_sim / class_width).astype(int)
    obs_classes = np.floor(adjusted_obs / class_width).astype(int)

    sim_classes = sim_classes % n_classes
    obs_classes = obs_classes % n_classes

    correct = np.sum(sim_classes == obs_classes)
    total = len(sim_classes)
    class_accuracy = (correct / total) * 100 if total > 0 else 0
    return class_accuracy, sim_classes, obs_classes


def get_direction_name_16(class_idx):
    """获取16方位名称"""
    directions = ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE",
                  "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]
    return directions[class_idx % 16]


def get_direction_name_8(class_idx):
    """获取8方位名称"""
    directions = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]
    return directions[class_idx % 8]


# 测试案例
print("="*60)
print("测试风向分类逻辑")
print("="*60)

# 测试1：边界情况
test_angles = [0, 5, 10, 15, 20, 350, 355, 359]
print("\n测试1：北向附近的角度")
print("-"*40)

for angle in test_angles:
    sim_dir = np.array([angle])
    obs_dir = np.array([angle])

    # 16方位
    acc_orig_16, sim_cls_orig_16, obs_cls_orig_16 = calculate_wind_direction_class_accuracy_original(sim_dir, obs_dir, 16)
    acc_fix_16, sim_cls_fix_16, obs_cls_fix_16 = calculate_wind_direction_class_accuracy_fixed(sim_dir, obs_dir, 16)

    # 8方位
    acc_orig_8, sim_cls_orig_8, obs_cls_orig_8 = calculate_wind_direction_class_accuracy_original(sim_dir, obs_dir, 8)
    acc_fix_8, sim_cls_fix_8, obs_cls_fix_8 = calculate_wind_direction_class_accuracy_fixed(sim_dir, obs_dir, 8)

    print(f"\n角度: {angle}°")
    print(f"  16方位 - 原始: 类别{sim_cls_orig_16[0]:2d} ({get_direction_name_16(sim_cls_orig_16[0])})")
    print(f"  16方位 - 修复: 类别{sim_cls_fix_16[0]:2d} ({get_direction_name_16(sim_cls_fix_16[0])})")
    print(f"  8方位  - 原始: 类别{sim_cls_orig_8[0]:2d} ({get_direction_name_8(sim_cls_orig_8[0])})")
    print(f"  8方位  - 修复: 类别{sim_cls_fix_8[0]:2d} ({get_direction_name_8(sim_cls_fix_8[0])})")

# 测试2：不同误差下的准确率
print("\n\n测试2：不同误差下的准确率对比")
print("-"*40)

np.random.seed(42)
n_samples = 1000

# 生成基准风向
base_directions = np.random.uniform(0, 360, n_samples)

# 测试不同的误差水平
error_levels = [5, 10, 15, 20, 25, 30]

print("\n误差(°) | 16方位准确率(%) | 8方位准确率(%)")
print("-"*50)

for error in error_levels:
    # 添加误差
    noise = np.random.normal(0, error, n_samples)
    sim_directions = (base_directions + noise) % 360

    # 计算准确率
    acc_16, _, _ = calculate_wind_direction_class_accuracy_fixed(sim_directions, base_directions, 16)
    acc_8, _, _ = calculate_wind_direction_class_accuracy_fixed(sim_directions, base_directions, 8)

    print(f"  {error:2d}    |     {acc_16:5.1f}       |    {acc_8:5.1f}")

    # 检查逻辑是否正确
    if acc_8 < acc_16:
        print(f"  ⚠️ 警告: 8方位准确率({acc_8:.1f}%) 低于 16方位准确率({acc_16:.1f}%)!")

# 测试3：具体方位的边界
print("\n\n测试3：各方位中心和边界")
print("-"*40)

# 16方位的中心和边界
print("\n16方位:")
for i in range(16):
    center = i * 22.5
    start = (center - 11.25) % 360
    end = (center + 11.25) % 360

    # 测试中心点
    sim = np.array([center])
    _, sim_cls, _ = calculate_wind_direction_class_accuracy_fixed(sim, sim, 16)

    print(f"  {get_direction_name_16(i):3s}: 中心={center:5.1f}°, 范围=[{start:5.1f}°-{end:5.1f}°], 分类={sim_cls[0]}")

print("\n8方位:")
for i in range(8):
    center = i * 45
    start = (center - 22.5) % 360
    end = (center + 22.5) % 360

    # 测试中心点
    sim = np.array([center])
    _, sim_cls, _ = calculate_wind_direction_class_accuracy_fixed(sim, sim, 8)

    print(f"  {get_direction_name_8(i):3s}: 中心={center:5.1f}°, 范围=[{start:5.1f}°-{end:5.1f}°], 分类={sim_cls[0]}")

# 测试4：验证逻辑一致性
print("\n\n测试4：验证相同风向差在不同分类下的表现")
print("-"*40)

# 测试一组具有固定偏差的数据
obs_dirs = np.array([0, 45, 90, 135, 180, 225, 270, 315])
offsets = [10, 20, 30, 40]

for offset in offsets:
    sim_dirs = (obs_dirs + offset) % 360

    acc_16, _, _ = calculate_wind_direction_class_accuracy_fixed(sim_dirs, obs_dirs, 16)
    acc_8, _, _ = calculate_wind_direction_class_accuracy_fixed(sim_dirs, obs_dirs, 8)

    print(f"偏移 {offset}°: 16方位={acc_16:.1f}%, 8方位={acc_8:.1f}%")

    if acc_8 < acc_16:
        print(f"  ⚠️ 问题: 8方位准确率应该更高!")