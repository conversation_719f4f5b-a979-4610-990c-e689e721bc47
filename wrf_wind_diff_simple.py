#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WRF风速差值计算与可视化（简化版）
计算两个WRF输出文件的风速差值，并生成等值线图
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
from netCDF4 import Dataset
from wrf import getvar, to_np, latlon_coords
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False


def read_wrf_wind(filename, time_idx=0, level=0):
    """
    读取WRF输出文件中的风场数据
    """
    print(f"  正在打开文件: {filename}")
    ncfile = Dataset(filename)
    
    # 尝试读取10米风场
    try:
        u10 = getvar(ncfile, "U10", timeidx=time_idx)
        v10 = getvar(ncfile, "V10", timeidx=time_idx)
        wspd10 = np.sqrt(u10**2 + v10**2)
        
        # 获取经纬度坐标
        lats, lons = latlon_coords(u10)
        
        ncfile.close()
        
        return {
            'u': to_np(u10),
            'v': to_np(v10),
            'wspd': to_np(wspd10),
            'lat': to_np(lats),
            'lon': to_np(lons),
            'height': '10m'
        }
    except Exception as e:
        print(f"  未找到10米风场: {e}")
        print("  尝试读取模式层风场...")
    
    # 读取模式层风场
    u = getvar(ncfile, "ua", timeidx=time_idx)
    v = getvar(ncfile, "va", timeidx=time_idx)
    
    # 选择特定层次
    if len(u.shape) == 3:
        u = u[level, :, :]
        v = v[level, :, :]
    
    wspd = np.sqrt(u**2 + v**2)
    
    # 获取经纬度坐标
    lats, lons = latlon_coords(u)
    
    ncfile.close()
    
    return {
        'u': to_np(u),
        'v': to_np(v),
        'wspd': to_np(wspd),
        'lat': to_np(lats),
        'lon': to_np(lons),
        'height': f'Level {level}'
    }


def calculate_wind_difference(wrf1_data, wrf2_data):
    """
    计算两个WRF文件的风速差值
    """
    return {
        'u_diff': wrf2_data['u'] - wrf1_data['u'],
        'v_diff': wrf2_data['v'] - wrf1_data['v'],
        'wspd_diff': wrf2_data['wspd'] - wrf1_data['wspd'],
        'lat': wrf1_data['lat'],
        'lon': wrf1_data['lon']
    }


def plot_wind_difference_simple(diff_data, wrf1_file, wrf2_file, save_path=None):
    """
    绘制简化的风速差值图（不使用Cartopy）
    """
    print("  创建图形...")
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 获取数据
    lon = diff_data['lon']
    lat = diff_data['lat']
    wspd_diff = diff_data['wspd_diff']
    
    # 设置色标范围
    vmax = np.abs(wspd_diff).max()
    vmin = -vmax
    levels = np.linspace(vmin, vmax, 21)
    
    print("  绘制等值线...")
    # 绘制填充等值线
    cf = ax.contourf(lon, lat, wspd_diff, levels=levels, 
                     cmap='RdBu_r', extend='both')
    
    # 添加等值线
    cs = ax.contour(lon, lat, wspd_diff, levels=levels[::2], 
                    colors='black', linewidths=0.5, alpha=0.5)
    ax.clabel(cs, inline=True, fontsize=8, fmt='%.1f')
    
    # 添加颜色条
    cbar = plt.colorbar(cf, ax=ax, orientation='vertical', pad=0.05, shrink=0.8)
    cbar.set_label('风速差值 (m/s)', fontsize=12)
    
    # 设置坐标轴标签
    ax.set_xlabel('经度', fontsize=12)
    ax.set_ylabel('纬度', fontsize=12)
    
    # 添加网格
    ax.grid(True, linestyle='--', alpha=0.3)
    
    # 设置标题
    file1_name = wrf1_file.split('\\')[-1] if '\\' in wrf1_file else wrf1_file.split('/')[-1]
    file2_name = wrf2_file.split('\\')[-1] if '\\' in wrf2_file else wrf2_file.split('/')[-1]
    ax.set_title(f'风速差值分析\n({file2_name} - {file1_name})', 
                fontsize=14, fontweight='bold')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存或显示图形
    if save_path:
        print(f"  保存图片至: {save_path}")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    print("  显示图形...")
    plt.show()
    
    return fig, ax


def plot_comparison_simple(wrf1_data, wrf2_data, diff_data,
                          wrf1_file, wrf2_file, save_path=None):
    """
    绘制简化的对比图（三个子图）
    """
    print("  创建对比图...")
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))

    # 数据列表
    data_list = [wrf1_data, wrf2_data, diff_data]
    file1_name = wrf1_file.split('\\')[-1] if '\\' in wrf1_file else wrf1_file.split('/')[-1]
    file2_name = wrf2_file.split('\\')[-1] if '\\' in wrf2_file else wrf2_file.split('/')[-1]

    titles = [f'WRF1: {file1_name}',
              f'WRF2: {file2_name}',
              '风速差值 (WRF2 - WRF1)']

    # 计算所有WRF数据的统一最大值，确保色标一致
    all_wspd_max = max(wrf1_data['wspd'].max(), wrf2_data['wspd'].max())

    for idx, (ax, data, title) in enumerate(zip(axes, data_list, titles)):
        # 选择要绘制的数据
        if idx < 2:  # WRF原始数据
            plot_data = data['wspd']
            cmap = 'YlOrRd'
            # 使用统一的最大值，避免色标突变
            vmin = 0
            vmax = all_wspd_max * 1.05  # 稍微扩大范围
            label = '风速 (m/s)'
        else:  # 差值数据
            plot_data = data['wspd_diff']
            cmap = 'RdBu_r'
            vmax = np.abs(plot_data).max()
            vmin = -vmax
            label = '风速差值 (m/s)'

        # 绘制填充等值线
        cf = ax.contourf(data['lon'], data['lat'], plot_data,
                        levels=20, cmap=cmap, vmin=vmin, vmax=vmax,
                        extend='both')

        # 添加等值线
        cs = ax.contour(data['lon'], data['lat'], plot_data,
                       levels=10, colors='black', linewidths=0.3, alpha=0.5)

        # 添加颜色条
        cbar = plt.colorbar(cf, ax=ax, orientation='horizontal',
                           pad=0.1, shrink=0.8)
        cbar.set_label(label, fontsize=9)
        cbar.ax.tick_params(labelsize=8)

        # 设置颜色条刻度
        if idx < 2:  # WRF原始数据使用相同的刻度
            # 设置统一的刻度，从0到最大值
            n_ticks = 11  # 显示11个刻度
            tick_values = np.linspace(0, vmax, n_ticks)
            cbar.set_ticks(tick_values)
            cbar.set_ticklabels([f'{v:.1f}' for v in tick_values])

        # 设置坐标轴
        ax.set_xlabel('经度', fontsize=10)
        ax.set_ylabel('纬度', fontsize=10)
        ax.grid(True, linestyle='--', alpha=0.3)
        ax.set_title(title, fontsize=11, fontweight='bold')

    # 调整布局
    plt.tight_layout()

    # 保存或显示图形
    if save_path:
        print(f"  保存对比图至: {save_path}")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    print("  显示对比图...")
    plt.show()

    return fig, axes


def plot_wind_vectors(diff_data, save_path=None, skip=15):
    """
    单独绘制风矢量差值图
    """
    print("  创建风矢量图...")
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 稀疏化数据
    lon = diff_data['lon'][::skip, ::skip]
    lat = diff_data['lat'][::skip, ::skip]
    u_diff = diff_data['u_diff'][::skip, ::skip]
    v_diff = diff_data['v_diff'][::skip, ::skip]
    wspd_diff = diff_data['wspd_diff']
    
    # 绘制背景风速差值
    cf = ax.contourf(diff_data['lon'], diff_data['lat'], wspd_diff,
                     levels=20, cmap='RdBu_r', alpha=0.6, extend='both')
    
    # 绘制风矢量
    Q = ax.quiver(lon, lat, u_diff, v_diff,
                  scale=50, scale_units='inches',
                  width=0.003, headwidth=3, headlength=4)
    
    # 添加风矢量参考
    ax.quiverkey(Q, 0.9, 0.95, 5, '5 m/s', 
                coordinates='axes', fontproperties={'size': 10})
    
    # 添加颜色条
    cbar = plt.colorbar(cf, ax=ax, orientation='vertical', pad=0.05)
    cbar.set_label('风速差值 (m/s)', fontsize=12)
    
    # 设置标签和标题
    ax.set_xlabel('经度', fontsize=12)
    ax.set_ylabel('纬度', fontsize=12)
    ax.set_title('风矢量差值分布', fontsize=14, fontweight='bold')
    ax.grid(True, linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        print(f"  保存风矢量图至: {save_path}")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()
    
    return fig, ax


def main():
    """
    主函数
    """
    # 设置WRF文件路径
    wrf1_file = r"E:\wrf_out7\wrfout_d04_2022-07-01_14_00_00"
    wrf2_file = r"E:\wrf_out5\wrfout_d04_2022-07-01_14_00_00"
    
    # 设置参数
    time_idx = 0  # 时间索引
    level = 0     # 垂直层次（0为最低层）
    
    print("="*60)
    print("WRF风速差值分析程序")
    print("="*60)
    
    print("\n1. 读取WRF数据...")
    try:
        wrf1_data = read_wrf_wind(wrf1_file, time_idx, level)
        print(f"  ✓ 成功读取WRF1数据 (高度: {wrf1_data['height']})")
        print(f"    数据维度: {wrf1_data['wspd'].shape}")
        
        wrf2_data = read_wrf_wind(wrf2_file, time_idx, level)
        print(f"  ✓ 成功读取WRF2数据 (高度: {wrf2_data['height']})")
        print(f"    数据维度: {wrf2_data['wspd'].shape}")
    except Exception as e:
        print(f"  ✗ 读取WRF文件出错: {e}")
        print("    请检查文件路径是否正确")
        return
    
    print("\n2. 计算风速差值...")
    diff_data = calculate_wind_difference(wrf1_data, wrf2_data)
    
    # 输出统计信息
    print("\n3. 风速差值统计:")
    print(f"  最大正差值: {diff_data['wspd_diff'].max():.2f} m/s")
    print(f"  最大负差值: {diff_data['wspd_diff'].min():.2f} m/s")
    print(f"  平均差值: {diff_data['wspd_diff'].mean():.2f} m/s")
    print(f"  差值标准差: {diff_data['wspd_diff'].std():.2f} m/s")
    
    # 绘制图形
    print("\n4. 绘制风速差值图...")
    try:
        plot_wind_difference_simple(diff_data, wrf1_file, wrf2_file,
                                   save_path="wind_difference_simple.png")
        print("  ✓ 风速差值图绘制完成")
    except Exception as e:
        print(f"  ✗ 绘制差值图失败: {e}")
    
    print("\n5. 绘制对比图...")
    try:
        plot_comparison_simple(wrf1_data, wrf2_data, diff_data,
                             wrf1_file, wrf2_file,
                             save_path="wind_comparison_simple.png")
        print("  ✓ 对比图绘制完成")
    except Exception as e:
        print(f"  ✗ 绘制对比图失败: {e}")
    
    print("\n6. 绘制风矢量差值图...")
    try:
        plot_wind_vectors(diff_data, save_path="wind_vectors.png")
        print("  ✓ 风矢量图绘制完成")
    except Exception as e:
        print(f"  ✗ 绘制风矢量图失败: {e}")
    
    print("\n" + "="*60)
    print("分析完成！")
    print("="*60)


if __name__ == "__main__":
    main()