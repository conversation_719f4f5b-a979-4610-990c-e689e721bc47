import numpy as np

def calculate_wind_direction_class_accuracy_debug(sim_dir, obs_dir, n_classes=16):
    """带调试信息的风向分类准确率计算"""
    class_width = 360 / n_classes
    half_width = class_width / 2

    # 将风向调整半个分类宽度，使北向居中
    adjusted_sim = (sim_dir + half_width) % 360
    adjusted_obs = (obs_dir + half_width) % 360

    # 计算分类索引
    sim_classes = np.floor(adjusted_sim / class_width).astype(int)
    obs_classes = np.floor(adjusted_obs / class_width).astype(int)

    # 确保索引在有效范围内
    sim_classes = sim_classes % n_classes
    obs_classes = obs_classes % n_classes

    # 计算准确率
    correct = np.sum(sim_classes == obs_classes)
    total = len(sim_classes)
    class_accuracy = (correct / total) * 100 if total > 0 else 0

    # 调试：找出不匹配的案例
    mismatches = sim_classes != obs_classes
    if np.any(mismatches):
        print(f"\n{n_classes}方位不匹配的案例（前10个）:")
        mismatch_indices = np.where(mismatches)[0][:10]
        for idx in mismatch_indices:
            print(f"  索引{idx}: 观测={obs_dir[idx]:.1f}度(类{obs_classes[idx]}), "
                  f"模拟={sim_dir[idx]:.1f}度(类{sim_classes[idx]})")

    return class_accuracy, correct, total


# 创建一个可能导致问题的特殊数据集
print("="*60)
print("测试特殊情况：系统性偏差")
print("="*60)

# 生成一些在8方位边界附近的数据
# 8方位的边界是：22.5, 67.5, 112.5, 157.5, 202.5, 247.5, 292.5, 337.5
# 16方位的边界是：11.25, 33.75, 56.25, 78.75...每22.5度一个

# 案例1：观测在8方位中心，模拟有偏差
print("\n案例1：观测在8方位中心（0,45,90...），模拟偏差20度")
obs_8_centers = np.array([0, 45, 90, 135, 180, 225, 270, 315])
sim_with_bias = (obs_8_centers + 20) % 360

acc_16, correct_16, total_16 = calculate_wind_direction_class_accuracy_debug(sim_with_bias, obs_8_centers, 16)
acc_8, correct_8, total_8 = calculate_wind_direction_class_accuracy_debug(sim_with_bias, obs_8_centers, 8)

print(f"\n结果:")
print(f"16方位: {correct_16}/{total_16} = {acc_16:.1f}%")
print(f"8方位:  {correct_8}/{total_8} = {acc_8:.1f}%")

# 案例2：观测在16方位边界，模拟有偏差
print("\n案例2：观测在16方位边界（11.25,33.75...），模拟偏差15度")
obs_16_boundaries = np.array([11.25, 33.75, 56.25, 78.75, 101.25, 123.75, 146.25, 168.75])
sim_with_bias = (obs_16_boundaries + 15) % 360

acc_16, correct_16, total_16 = calculate_wind_direction_class_accuracy_debug(sim_with_bias, obs_16_boundaries, 16)
acc_8, correct_8, total_8 = calculate_wind_direction_class_accuracy_debug(sim_with_bias, obs_16_boundaries, 8)

print(f"\n结果:")
print(f"16方位: {correct_16}/{total_16} = {acc_16:.1f}%")
print(f"8方位:  {correct_8}/{total_8} = {acc_8:.1f}%")

# 案例3：真实场景模拟 - 主导风向
print("\n案例3：主导风向场景（70%北风，30%南风），误差标准差20度")
np.random.seed(42)
n = 100
# 70%北风(0度附近)，30%南风(180度附近)
north_winds = np.random.normal(0, 10, 70)
south_winds = np.random.normal(180, 10, 30)
obs_dirs = np.concatenate([north_winds, south_winds]) % 360

# 添加模拟误差
errors = np.random.normal(0, 20, n)
sim_dirs = (obs_dirs + errors) % 360

print(f"观测风向统计: 均值={np.mean(obs_dirs):.1f}, 标准差={np.std(obs_dirs):.1f}")
print(f"模拟风向统计: 均值={np.mean(sim_dirs):.1f}, 标准差={np.std(sim_dirs):.1f}")

acc_16, correct_16, total_16 = calculate_wind_direction_class_accuracy_debug(sim_dirs, obs_dirs, 16)
acc_8, correct_8, total_8 = calculate_wind_direction_class_accuracy_debug(sim_dirs, obs_dirs, 8)

print(f"\n结果:")
print(f"16方位: {correct_16}/{total_16} = {acc_16:.1f}%")
print(f"8方位:  {correct_8}/{total_8} = {acc_8:.1f}%")

if acc_8 < acc_16:
    print("\n!!! 发现问题：8方位准确率低于16方位 !!!")
    print("这不符合逻辑，需要进一步调查")

# 最终检查：完整测试
print("\n"+"="*60)
print("完整测试：多种误差水平")
print("="*60)
print("\n误差(度) | 16方位(%) | 8方位(%) | 8-16差值")
print("-"*45)

for error_std in [5, 10, 15, 20, 25, 30]:
    np.random.seed(100)
    n = 500
    obs_dirs = np.random.uniform(0, 360, n)
    errors = np.random.normal(0, error_std, n)
    sim_dirs = (obs_dirs + errors) % 360

    acc_16, _, _ = calculate_wind_direction_class_accuracy_debug(sim_dirs, obs_dirs, 16)
    acc_8, _, _ = calculate_wind_direction_class_accuracy_debug(sim_dirs, obs_dirs, 8)
    diff = acc_8 - acc_16

    status = "ERROR" if acc_8 < acc_16 else "OK"
    print(f"   {error_std:2d}    |   {acc_16:5.1f}   |  {acc_8:5.1f}   | {diff:+6.1f}  {status}")