import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr
import sys
import os

# 全局静态配置变量
WRF_OUT_BASE = r"E:\wrf_out"  # WRF输出基础路径
DOM_ID = 3  # 嵌套域ID

# 批处理配置
WRF_SUFFIXES = [str(i) for i in range(2, 15)]  # wrf_out2 到 wrf_out11
STATION_TYPES = ["30M", "75M"]  # 要处理的站点类型


# 动态生成文件路径
def get_file_paths(wrf_suffix, station_type):
    """根据配置动态生成文件路径"""
    wrf_folder = f"{WRF_OUT_BASE}{wrf_suffix}"
    simulated_file = os.path.join(
        wrf_folder, "csv", f"wrf_station_data_5min_parallel_d{DOM_ID:02}.csv"
    )
    observed_file = os.path.join(r"F:\项目\Code\csv", f"202207_{station_type}.csv")
    return simulated_file, observed_file


# 动态配置风速和风向列
def get_wind_column_config(station_type):
    """根据站点类型配置风速和风向列"""
    if station_type == "75M":
        return "75M_75m_wind_speed_ms", "Wind speed", "75M_75m_wind_dir_deg", "Wind_Dir"
    else:  # 30M
        return "30M_30m_wind_speed_ms", "Wind speed", "30M_30m_wind_dir_deg", "Wind_Dir"


# 常量定义
SIMULATED_TIME_COLUMN = "local_time"
OBSERVED_TIME_COLUMN = "Time"
SIMULATED_WIND_COLUMN = 13  # 保留数字索引作为备用
OBSERVED_WIND_COLUMN = 7  # WS (m/s) 观测风速列索引
TIME_FORMAT = "%Y-%m-%d %H:%M:%S"  # 更新时间格式：2022-07-01 08:00:00


def read_and_validate_data(simulated_file, observed_file):
    """
    读取和验证两个分离的CSV/Excel文件
    """
    print(f"Reading simulated data from: {simulated_file}")
    sys.stdout.flush()
    try:
        sim_df = pd.read_csv(simulated_file)
        print(f"Simulated data shape: {sim_df.shape}")
    except Exception as e:
        print(f"Error reading simulated file: {e}")
        return None, None

    print(f"Reading observed data from: {observed_file}")
    sys.stdout.flush()
    try:
        obs_df = pd.read_csv(
            observed_file, low_memory=False
        )  # 添加low_memory=False避免警告
        print(f"Observed data shape: {obs_df.shape}")
    except Exception as e:
        print(f"Error reading observed file: {e}")
        return None, None

    return sim_df, obs_df


def align_data_by_time(sim_df, obs_df):
    """
    通过时间列对齐两个数据框
    """
    print("Aligning data by time...")
    sys.stdout.flush()

    # 转换时间列为datetime类型
    try:
        # 首先尝试使用配置的格式解析模拟数据
        sim_df[SIMULATED_TIME_COLUMN] = pd.to_datetime(
            sim_df[SIMULATED_TIME_COLUMN], format=TIME_FORMAT
        )
    except Exception as e:
        print(f"Using automatic parsing for simulated data time column: {e}")
        sim_df[SIMULATED_TIME_COLUMN] = pd.to_datetime(sim_df[SIMULATED_TIME_COLUMN])

    try:
        # 观测数据使用自动解析（因为格式可能不同）
        obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(obs_df[OBSERVED_TIME_COLUMN])
        print(f"Successfully parsed observed time column with automatic detection")
    except Exception as e:
        print(f"Error parsing observed time column: {e}")
        # 如果自动解析失败，尝试常见格式
        try:
            obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(
                obs_df[OBSERVED_TIME_COLUMN], format="%Y-%m-%d %H:%M:%S"
            )
            print("Successfully parsed observed time with ISO format")
        except:
            print("Using infer_datetime_format as last resort")
            obs_df[OBSERVED_TIME_COLUMN] = pd.to_datetime(
                obs_df[OBSERVED_TIME_COLUMN], infer_datetime_format=True
            )

    # 基于时间列合并数据
    merged_df = pd.merge(
        sim_df,
        obs_df,
        left_on=SIMULATED_TIME_COLUMN,
        right_on=OBSERVED_TIME_COLUMN,
        how="inner",
        suffixes=("_sim", "_obs"),
    )

    print(f"Merged data shape: {merged_df.shape}")
    return merged_df


def align_data_by_index(sim_df, obs_df):
    """
    通过索引对齐两个数据框（当没有时间列时使用）
    """
    print("Aligning data by index...")
    sys.stdout.flush()

    # 确保两个数据框长度一致
    min_length = min(len(sim_df), len(obs_df))
    sim_df_aligned = sim_df.iloc[:min_length].copy()
    obs_df_aligned = obs_df.iloc[:min_length].copy()

    print(f"Aligned data length: {min_length}")
    return sim_df_aligned, obs_df_aligned


def extract_wind_data(
    merged_df,
    simulated_wind_column_name,
    observed_wind_column_name,
    simulated_dir_column_name=None,
    observed_dir_column_name=None,
):
    """
    从合并后的数据框中提取风速和风向数据
    """
    print("Extracting wind speed and direction data...")
    sys.stdout.flush()

    try:
        print("Available columns after merge:")
        for i, col in enumerate(merged_df.columns):
            print(f"  {i}: {col}")

        # 直接使用列名来提取数据，更可靠
        sim_wind_col = None
        obs_wind_col = None

        # 查找模拟数据的风速列 - 使用动态配置的列名
        for col in merged_df.columns:
            if simulated_wind_column_name in col:
                sim_wind_col = col
                break

        # 查找观测数据的风速列 - 使用动态配置的列名
        for col in merged_df.columns:
            if observed_wind_column_name in col:
                obs_wind_col = col
                break

        # 查找风向列（如果提供）
        sim_dir_col = None
        obs_dir_col = None

        if simulated_dir_column_name:
            for col in merged_df.columns:
                if simulated_dir_column_name in col:
                    sim_dir_col = col
                    break

        if observed_dir_column_name:
            for col in merged_df.columns:
                if observed_dir_column_name in col:
                    obs_dir_col = col
                    break

        if sim_wind_col is None or obs_wind_col is None:
            print(f"Could not find wind speed columns:")
            print(f"  Simulated: {sim_wind_col}")
            print(f"  Observed: {obs_wind_col}")
            return None, None, None, None

        simulated_wind_speed = merged_df[sim_wind_col]
        observed_wind_speed = merged_df[obs_wind_col]

        # 提取风向数据（如果可用）
        simulated_wind_dir = merged_df[sim_dir_col] if sim_dir_col else None
        observed_wind_dir = merged_df[obs_dir_col] if obs_dir_col else None

        print(f"Using simulated wind speed column: {sim_wind_col}")
        print(f"Using observed wind speed column: {obs_wind_col}")
        if sim_dir_col:
            print(f"Using simulated wind direction column: {sim_dir_col}")
        if obs_dir_col:
            print(f"Using observed wind direction column: {obs_dir_col}")
        print(f"Simulated wind speed shape: {simulated_wind_speed.shape}")
        print(f"Observed wind speed shape: {observed_wind_speed.shape}")
        print(f"Simulated wind speed sample: {simulated_wind_speed.head()}")
        print(f"Observed wind speed sample: {observed_wind_speed.head()}")

        return (
            simulated_wind_speed,
            observed_wind_speed,
            simulated_wind_dir,
            observed_wind_dir,
        )

    except Exception as e:
        print(f"Error extracting wind speed data: {e}")
        return None, None, None, None


def plot_wind_vectors(
    simulated_wind_speed,
    observed_wind_speed,
    simulated_wind_dir,
    observed_wind_dir,
    merged_df,
    wrf_suffix,
    station_type,
    output_file=None,
):
    """
    绘制风矢量图
    """
    print("Generating wind vector plot...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到模拟数据文件夹
    if output_file is None:
        import os

        wrf_folder = f"{WRF_OUT_BASE}{wrf_suffix}"
        sim_dir = os.path.join(wrf_folder, "csv")
        output_file = os.path.join(
            sim_dir, f"wind_vector_comparison_d{DOM_ID:02}_{station_type}.png"
        )

    # 检查是否有风向数据
    if simulated_wind_dir is None or observed_wind_dir is None:
        print("Warning: Wind direction data not available, skipping wind vector plot")
        return

    # 获取时间数据用于横坐标
    time_data = merged_df[SIMULATED_TIME_COLUMN]

    # 转换为数值类型并清理数据
    sim_speed = pd.to_numeric(simulated_wind_speed, errors="coerce")
    obs_speed = pd.to_numeric(observed_wind_speed, errors="coerce")
    sim_dir = pd.to_numeric(simulated_wind_dir, errors="coerce")
    obs_dir = pd.to_numeric(observed_wind_dir, errors="coerce")

    # 转换风向为弧度（气象风向转数学风向）
    # 气象风向：风来的方向，0°为北风；数学风向：向东为0°，逆时针为正
    sim_dir_rad = np.radians(90 - sim_dir)  # 转换为数学风向弧度
    obs_dir_rad = np.radians(90 - obs_dir)

    # 计算风矢量分量 (U: 东西向, V: 南北向)
    sim_u = sim_speed * np.cos(sim_dir_rad)
    sim_v = sim_speed * np.sin(sim_dir_rad)
    obs_u = obs_speed * np.cos(obs_dir_rad)
    obs_v = obs_speed * np.sin(obs_dir_rad)

    # 创建单个图形用于风矢量显示
    fig, ax = plt.subplots(1, 1, figsize=(15, 8))

    # 风矢量对比图 - 使用quiver绘制风矢量
    # 为了清晰显示，每隔N个数据点绘制一个箭头
    step = max(1, len(time_data) // 30)  # 最多显示30个箭头
    time_subset = time_data[::step]
    sim_u_subset = sim_u[::step]
    sim_v_subset = sim_v[::step]
    obs_u_subset = obs_u[::step]
    obs_v_subset = obs_v[::step]

    # 创建数值化的时间索引用于箭头绘制
    import matplotlib.dates as mdates

    time_numeric = mdates.date2num(time_subset)

    # 创建Y坐标 - 模拟数据在上方，观测数据在下方
    y_sim = np.ones(len(time_numeric)) * 1.5  # 模拟数据位置
    y_obs = np.ones(len(time_numeric)) * 0.5  # 观测数据位置

    # 绘制模拟风矢量（蓝色）
    valid_sim = ~(np.isnan(sim_u_subset) | np.isnan(sim_v_subset))
    if np.any(valid_sim):
        q1 = ax.quiver(
            time_numeric[valid_sim],
            y_sim[valid_sim],
            sim_u_subset[valid_sim],
            sim_v_subset[valid_sim],
            scale=100,
            scale_units="width",
            width=0.003,
            color="blue",
            alpha=0.8,
            label="Simulated",
        )

    # 绘制观测风矢量（红色）
    valid_obs = ~(np.isnan(obs_u_subset) | np.isnan(obs_v_subset))
    if np.any(valid_obs):
        q2 = ax.quiver(
            time_numeric[valid_obs],
            y_obs[valid_obs],
            obs_u_subset[valid_obs],
            obs_v_subset[valid_obs],
            scale=100,
            scale_units="width",
            width=0.003,
            color="red",
            alpha=0.8,
            label="Observed",
        )

    # 添加参考箭头显示比例尺
    """ref_speed = 5  # 5 m/s 参考风速
    ax.quiver(
        time_numeric[0],
        2.2,
        ref_speed,
        0,
        scale=100,
        scale_units="width",
        width=0.004,
        color="black",
        alpha=0.9,
    )
    ax.text(
        time_numeric[0], 2.4, f"{ref_speed} m/s", ha="left", va="bottom", fontsize=10
    )"""

    ax.set_ylabel("Wind Vectors")
    ax.set_xlabel("Time")
    ax.set_title(f"Wind Vector Comparison - {station_type})")
    ax.set_ylim(0, 2.6)
    ax.grid(True, alpha=0.3)

    # 设置Y轴标签
    ax.set_yticks([0.5, 1.5])
    ax.set_yticklabels(["Observed", "Simulated"], rotation=90)

    # 添加图例
    legend_elements = []
    if np.any(valid_sim):
        legend_elements.append(
            plt.Line2D(
                [0],
                [0],
                marker=">",
                markersize=8,
                color="blue",
                linestyle="None",
                label="Simulated",
            )
        )
    if np.any(valid_obs):
        legend_elements.append(
            plt.Line2D(
                [0],
                [0],
                marker=">",
                markersize=8,
                color="red",
                linestyle="None",
                label="Observed",
            )
        )
    if legend_elements:
        ax.legend(handles=legend_elements, loc="upper right")

    # 设置时间格式显示
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    ax.tick_params(axis="x", rotation=0)

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches="tight")
    print(f"Wind vector plot saved to '{output_file}'")
    plt.close()


def plot_comparison(
    simulated_wind_speed,
    observed_wind_speed,
    merged_df,
    wrf_suffix,
    station_type,
    output_file=None,
):
    """
    绘制风速对比图
    """
    print("Generating wind speed comparison plot...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到模拟数据文件夹
    if output_file is None:
        import os

        wrf_folder = f"{WRF_OUT_BASE}{wrf_suffix}"
        sim_dir = os.path.join(wrf_folder, "csv")
        output_file = os.path.join(
            sim_dir, f"wind_speed_comparison_d{DOM_ID:02}_{station_type}.png"
        )

    # 获取时间数据用于横坐标
    time_data = merged_df[SIMULATED_TIME_COLUMN]

    plt.figure(figsize=(15, 6))
    plt.plot(
        time_data,
        simulated_wind_speed,
        label="Simulated Wind Speed",
        linewidth=1,
        alpha=0.8,
    )
    plt.plot(
        time_data,
        observed_wind_speed,
        label="Observed Wind Speed",
        linewidth=1,
        alpha=0.8,
    )

    plt.xlabel("Time")
    plt.ylabel("Wind Speed (m/s)")
    plt.title(f"Comparison of Simulated vs Observed Wind Speed - {station_type}")
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 设置时间格式显示
    import matplotlib.dates as mdates

    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
    plt.gca().xaxis.set_major_locator(
        mdates.HourLocator(interval=6)
    )  # 每6小时显示一个标签
    plt.xticks(rotation=0)

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches="tight")
    print(f"Plot saved to '{output_file}'")


def calculate_wind_direction_hit_rate(sim_dir, obs_dir, thresholds=[10, 22.5, 45]):
    """
    计算风向命中率（不同阈值下的准确率）

    Args:
        sim_dir: 模拟风向数组
        obs_dir: 观测风向数组
        thresholds: 阈值列表（度），默认[10°, 22.5°, 45°]
                   10° = ±10度以内算命中（非常精确）
                   22.5° = ±22.5度以内算命中（16方位中相邻方位）
                   45° = ±45度以内算命中（8方位中相邻方位）

    Returns:
        hit_rates: 各阈值下的命中率字典
    """
    # 计算风向差（考虑环形特性）
    dir_diff = calculate_wind_direction_difference(sim_dir, obs_dir)
    abs_diff = np.abs(dir_diff)

    hit_rates = {}
    for threshold in thresholds:
        # 计算在阈值内的比例
        hits = np.sum(abs_diff <= threshold)
        total = len(abs_diff)
        hit_rate = (hits / total) * 100 if total > 0 else 0
        hit_rates[threshold] = hit_rate

    return hit_rates


def calculate_wind_direction_class_accuracy(sim_dir, obs_dir, n_classes=16):
    """
    计算风向分类准确率（基于N个方向分类）

    Args:
        sim_dir: 模拟风向数组
        obs_dir: 观测风向数组
        n_classes: 方向分类数量（8=8方位，16=16方位）

    Returns:
        class_accuracy: 分类准确率
        confusion_matrix: 混淆矩阵
    """
    # 将风向转换为分类
    # 调整风向使得北向（0°）位于第一个分类的中心
    # 例如16方位时，N的范围是-11.25°到11.25°（等价于348.75°到11.25°）
    class_width = 360 / n_classes
    half_width = class_width / 2

    # 将风向调整半个分类宽度，使北向居中
    adjusted_sim = (sim_dir + half_width) % 360
    adjusted_obs = (obs_dir + half_width) % 360

    # 计算分类索引
    sim_classes = np.floor(adjusted_sim / class_width).astype(int)
    obs_classes = np.floor(adjusted_obs / class_width).astype(int)

    # 确保索引在有效范围内
    sim_classes = sim_classes % n_classes
    obs_classes = obs_classes % n_classes

    # 计算准确率
    correct = np.sum(sim_classes == obs_classes)
    total = len(sim_classes)
    class_accuracy = (correct / total) * 100 if total > 0 else 0

    # 创建混淆矩阵
    confusion_matrix = np.zeros((n_classes, n_classes))
    for i in range(len(sim_classes)):
        confusion_matrix[obs_classes[i], sim_classes[i]] += 1

    return class_accuracy, confusion_matrix


def calculate_metrics(
    simulated_wind_speed,
    observed_wind_speed,
    simulated_wind_dir=None,
    observed_wind_dir=None,
):
    """
    计算统计指标（包括风速和风向）
    """
    print("Calculating metrics...")
    sys.stdout.flush()

    # 风速统计
    # 转换为numpy数组并确保为数值类型
    sim_speed_array = pd.to_numeric(simulated_wind_speed, errors="coerce").values
    obs_speed_array = pd.to_numeric(observed_wind_speed, errors="coerce").values

    # 移除NaN值
    speed_mask = ~(np.isnan(sim_speed_array) | np.isnan(obs_speed_array))
    sim_speed_clean = sim_speed_array[speed_mask]
    obs_speed_clean = obs_speed_array[speed_mask]

    if len(sim_speed_clean) == 0:
        print("Warning: No valid wind speed data points after removing NaN values")
        return None, None, None, None, None, None, None, None, None

    # 计算风速指标
    speed_correlation, speed_p_value = pearsonr(sim_speed_clean, obs_speed_clean)
    speed_rmse = np.sqrt(mean_squared_error(obs_speed_clean, sim_speed_clean))
    speed_mae = mean_absolute_error(obs_speed_clean, sim_speed_clean)
    speed_mb = np.mean(sim_speed_clean - obs_speed_clean)

    # 风向统计（如果有风向数据）
    dir_mae = None
    dir_mb = None
    dir_rmse = None
    dir_correlation = None
    dir_hit_rates = None

    if simulated_wind_dir is not None and observed_wind_dir is not None:
        print("Calculating wind direction metrics...")

        sim_dir_array = pd.to_numeric(simulated_wind_dir, errors="coerce").values
        obs_dir_array = pd.to_numeric(observed_wind_dir, errors="coerce").values

        # 移除NaN值
        dir_mask = ~(np.isnan(sim_dir_array) | np.isnan(obs_dir_array))
        sim_dir_clean = sim_dir_array[dir_mask]
        obs_dir_clean = obs_dir_array[dir_mask]

        if len(sim_dir_clean) > 0:
            # 计算风向差（考虑环形特性）
            dir_diff = calculate_wind_direction_difference(sim_dir_clean, obs_dir_clean)

            # 风向平均绝对误差
            dir_mae = np.mean(np.abs(dir_diff))

            # 风向平均偏差
            dir_mb = calculate_mean_wind_direction_bias(dir_diff)

            # 风向均方根误差
            dir_rmse = np.sqrt(np.mean(dir_diff**2))

            # 风向相关系数（使用环形相关系数）
            dir_correlation = calculate_circular_correlation(
                sim_dir_clean, obs_dir_clean
            )

            # 计算风向命中率
            dir_hit_rates = calculate_wind_direction_hit_rate(
                sim_dir_clean, obs_dir_clean
            )

            # 计算16方位分类准确率
            class_accuracy_16, _ = calculate_wind_direction_class_accuracy(
                sim_dir_clean, obs_dir_clean, n_classes=16
            )

            # 计算8方位分类准确率
            class_accuracy_8, _ = calculate_wind_direction_class_accuracy(
                sim_dir_clean, obs_dir_clean, n_classes=8
            )

            print(f"  Wind direction MAE: {dir_mae:.2f}°")
            print(f"  Wind direction MB: {dir_mb:.2f}°")
            print(f"  Wind direction RMSE: {dir_rmse:.2f}°")
            print(f"  Wind direction correlation: {dir_correlation:.4f}")
            print(f"  Wind direction hit rates:")
            for threshold, rate in dir_hit_rates.items():
                print(f"    ±{threshold}°: {rate:.1f}%")
            print(f"  Wind direction class accuracy:")
            print(f"    16-class: {class_accuracy_16:.1f}%")
            print(f"    8-class: {class_accuracy_8:.1f}%")

            # 将分类准确率添加到hit_rates字典中
            if dir_hit_rates:
                dir_hit_rates["class_16"] = class_accuracy_16
                dir_hit_rates["class_8"] = class_accuracy_8

    return (
        speed_correlation,
        speed_rmse,
        speed_mae,
        speed_mb,
        dir_mae,
        dir_mb,
        dir_rmse,
        dir_correlation,
        dir_hit_rates,  # 新增返回值
    )


def calculate_wind_direction_difference(sim_dir, obs_dir):
    """
    计算风向差异，考虑环形特性（0-360度）
    返回范围在-180到180之间的角度差
    """
    diff = sim_dir - obs_dir

    # 将差异调整到-180到180度范围
    diff = np.where(diff > 180, diff - 360, diff)
    diff = np.where(diff < -180, diff + 360, diff)

    return diff


def calculate_mean_wind_direction_bias(dir_diff):
    """
    计算平均风向偏差，使用矢量平均法
    """
    # 转换为弧度
    dir_diff_rad = np.radians(dir_diff)

    # 计算矢量分量
    mean_sin = np.mean(np.sin(dir_diff_rad))
    mean_cos = np.mean(np.cos(dir_diff_rad))

    # 计算平均角度
    mean_angle = np.degrees(np.arctan2(mean_sin, mean_cos))

    return mean_angle


def calculate_circular_correlation(angles1, angles2):
    """
    计算环形相关系数（适用于风向等环形数据）
    """
    # 转换为弧度
    rad1 = np.radians(angles1)
    rad2 = np.radians(angles2)

    # 计算sin和cos分量
    sin1, cos1 = np.sin(rad1), np.cos(rad1)
    sin2, cos2 = np.sin(rad2), np.cos(rad2)

    # 计算相关系数
    r_sin = pearsonr(sin1, sin2)[0]
    r_cos = pearsonr(cos1, cos2)[0]

    # 组合相关系数
    r_circular = np.sqrt((r_sin**2 + r_cos**2) / 2)

    return r_circular


def plot_wind_direction_comparison(
    simulated_wind_dir,
    observed_wind_dir,
    merged_df,
    wrf_suffix,
    station_type,
    output_file=None,
):
    """
    绘制风向对比图（包括时间序列和风向玫瑰图）
    """
    print("Generating wind direction comparison plots...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到模拟数据文件夹
    if output_file is None:
        import os

        wrf_folder = f"{WRF_OUT_BASE}{wrf_suffix}"
        sim_dir = os.path.join(wrf_folder, "csv")
        output_file = os.path.join(
            sim_dir, f"wind_direction_comparison_d{DOM_ID:02}_{station_type}.png"
        )

    # 检查是否有风向数据
    if simulated_wind_dir is None or observed_wind_dir is None:
        print(
            "Warning: Wind direction data not available, skipping wind direction plots"
        )
        return

    # 获取时间数据用于横坐标
    time_data = merged_df[SIMULATED_TIME_COLUMN]

    # 转换为数值类型并清理数据
    sim_dir = pd.to_numeric(simulated_wind_dir, errors="coerce")
    obs_dir = pd.to_numeric(observed_wind_dir, errors="coerce")

    # 创建包含多个子图的图形
    fig = plt.figure(figsize=(18, 12))

    # 1. 风向时间序列对比
    ax1 = plt.subplot(3, 2, (1, 2))
    ax1.plot(
        time_data, sim_dir, label="Simulated", linewidth=1, alpha=0.8, color="blue"
    )
    ax1.plot(time_data, obs_dir, label="Observed", linewidth=1, alpha=0.8, color="red")
    ax1.set_xlabel("Time")
    ax1.set_ylabel("Wind Direction (°)")
    ax1.set_title(f"Wind Direction Time Series - {station_type}")
    ax1.set_ylim([0, 360])
    ax1.set_yticks([0, 45, 90, 135, 180, 225, 270, 315, 360])
    ax1.set_yticklabels(["N", "NE", "E", "SE", "S", "SW", "W", "NW", "N"])
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 设置时间格式显示
    import matplotlib.dates as mdates

    ax1.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=0)

    # 2. 风向差异时间序列
    ax2 = plt.subplot(3, 2, (3, 4))
    dir_diff = calculate_wind_direction_difference(sim_dir.values, obs_dir.values)
    ax2.plot(time_data, dir_diff, linewidth=1, alpha=0.8, color="green")
    ax2.axhline(y=0, color="black", linestyle="--", alpha=0.5)
    ax2.set_xlabel("Time")
    ax2.set_ylabel("Direction Difference (°)")
    ax2.set_title(f"Wind Direction Difference (Simulated - Observed)")
    ax2.set_ylim([-180, 180])
    ax2.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
    ax2.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=0)

    # 3. 模拟风向玫瑰图
    ax3 = plt.subplot(3, 2, 5, projection="polar")
    plot_wind_rose(sim_dir.dropna().values, ax3, "Simulated Wind Rose", "blue")

    # 4. 观测风向玫瑰图
    ax4 = plt.subplot(3, 2, 6, projection="polar")
    plot_wind_rose(obs_dir.dropna().values, ax4, "Observed Wind Rose", "red")

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches="tight")
    print(f"Wind direction comparison plots saved to '{output_file}'")
    plt.close()


def plot_wind_rose(directions, ax, title, color):
    """
    绘制风向玫瑰图
    """
    # 设置风向区间（16个方向）
    n_bins = 16
    theta_bins = np.linspace(0, 360, n_bins + 1)
    theta_labels = [
        "N",
        "NNE",
        "NE",
        "ENE",
        "E",
        "ESE",
        "SE",
        "SSE",
        "S",
        "SSW",
        "SW",
        "WSW",
        "W",
        "WNW",
        "NW",
        "NNW",
    ]

    # 统计每个方向的频率
    counts, bins = np.histogram(directions, bins=theta_bins)

    # 转换为百分比
    frequencies = counts / len(directions) * 100

    # 计算每个扇区的中心角度（弧度）
    theta = np.radians(np.linspace(0, 360, n_bins, endpoint=False))

    # 绘制玫瑰图
    width = 2 * np.pi / n_bins
    bars = ax.bar(
        theta,
        frequencies,
        width=width,
        bottom=0,
        color=color,
        alpha=0.7,
        edgecolor="black",
    )

    # 设置标题和标签
    ax.set_title(title, pad=20)
    ax.set_theta_direction(-1)  # 顺时针方向
    ax.set_theta_offset(np.pi / 2)  # 北向在顶部

    # 设置角度标签
    ax.set_xticks(theta)
    ax.set_xticklabels(theta_labels)

    # 设置径向刻度
    max_freq = max(frequencies) if len(frequencies) > 0 else 10
    ax.set_ylim(0, max_freq * 1.1)
    ax.set_ylabel("Frequency (%)", labelpad=30)

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 在每个条形上添加频率值
    for bar, freq, angle in zip(bars, frequencies, theta):
        if freq > 0:
            height = bar.get_height()
            ax.text(
                angle,
                height + max_freq * 0.02,
                f"{freq:.1f}%",
                ha="center",
                va="bottom",
                fontsize=8,
            )

    return ax


def save_results(
    correlation,
    rmse,
    mae,
    mb,
    wrf_suffix,
    station_type,
    dir_mae=None,
    dir_mb=None,
    dir_rmse=None,
    dir_correlation=None,
    dir_hit_rates=None,  # 新增参数
    output_file=None,
):
    """
    保存结果到文件（包括风速、风向统计和命中率）
    """
    print("Saving results to file...")
    sys.stdout.flush()

    # 如果未指定输出文件，保存到模拟数据文件夹
    if output_file is None:
        import os

        wrf_folder = f"{WRF_OUT_BASE}{wrf_suffix}"
        sim_dir = os.path.join(wrf_folder, "csv")
        output_file = os.path.join(
            sim_dir, f"wind_analysis_statistics_d{DOM_ID:02}_{station_type}.txt"
        )

    with open(output_file, "w") as f:
        f.write(f"Wind Analysis Results - WRF_OUT{wrf_suffix} - {station_type}\n")
        f.write("=" * 50 + "\n\n")

        # 风速统计
        f.write("WIND SPEED STATISTICS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Correlation Coefficient (R): {correlation:.4f}\n")
        f.write(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s\n")
        f.write(f"Mean Absolute Error (MAE): {mae:.4f} m/s\n")
        f.write(f"Mean Bias (MB): {mb:.4f} m/s\n")

        # 风向统计（如果可用）
        if dir_mae is not None:
            f.write("\n")
            f.write("WIND DIRECTION STATISTICS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Circular Correlation: {dir_correlation:.4f}\n")
            f.write(f"Root Mean Square Error (RMSE): {dir_rmse:.2f}°\n")
            f.write(f"Mean Absolute Error (MAE): {dir_mae:.2f}°\n")
            f.write(f"Mean Bias (MB): {dir_mb:.2f}°\n")

            # 风向命中率（如果可用）
            if dir_hit_rates:
                f.write("\n")
                f.write("WIND DIRECTION HIT RATES:\n")
                f.write("-" * 30 + "\n")
                for threshold in [10, 22.5, 45]:
                    if threshold in dir_hit_rates:
                        f.write(
                            f"±{threshold}° threshold: {dir_hit_rates[threshold]:.1f}%\n"
                        )
                if "class_16" in dir_hit_rates:
                    f.write(
                        f"16-direction class accuracy: {dir_hit_rates['class_16']:.1f}%\n"
                    )
                if "class_8" in dir_hit_rates:
                    f.write(
                        f"8-direction class accuracy: {dir_hit_rates['class_8']:.1f}%\n"
                    )

    print(f"Results saved to '{output_file}'")


def process_single_case(wrf_suffix, station_type):
    """
    处理单个WRF输出和站点类型的组合
    """
    print(f"\n{'='*60}")
    print(f"Processing WRF_OUT{wrf_suffix} - {station_type}")
    print(f"{'='*60}")

    # 获取文件路径和配置
    simulated_file, observed_file = get_file_paths(wrf_suffix, station_type)
    config = get_wind_column_config(station_type)
    simulated_wind_column_name, observed_wind_column_name = config[0], config[1]
    simulated_dir_column_name, observed_dir_column_name = config[2], (
        config[3] if len(config) >= 4 else (None, None)
    )

    print(f"Configuration:")
    print(f"  WRF output folder: {WRF_OUT_BASE}{wrf_suffix}")
    print(f"  Domain ID: d{DOM_ID:02}")
    print(f"  Station type: {station_type}")
    print(f"  Simulated file: {simulated_file}")
    print(f"  Observed file: {observed_file}")
    print(f"  Simulated wind column: {simulated_wind_column_name}")
    print(f"  Observed wind column: {observed_wind_column_name}")
    print(f"  Simulated wind direction column: {simulated_dir_column_name}")
    print(f"  Observed wind direction column: {observed_dir_column_name}")

    # 检查文件是否存在
    if not os.path.exists(simulated_file):
        print(f"❌ Simulated file not found: {simulated_file}")
        return False
    if not os.path.exists(observed_file):
        print(f"❌ Observed file not found: {observed_file}")
        return False

    # 读取数据
    sim_df, obs_df = read_and_validate_data(simulated_file, observed_file)
    if sim_df is None or obs_df is None:
        print(f"❌ Failed to read data files")
        return False

    # 通过时间列对齐数据
    merged_df = align_data_by_time(sim_df, obs_df)
    if merged_df is None or len(merged_df) == 0:
        print(f"❌ Failed to align data or no matching time points")
        return False

    # 提取风速和风向数据
    simulated_wind_speed, observed_wind_speed, simulated_wind_dir, observed_wind_dir = (
        extract_wind_data(
            merged_df,
            simulated_wind_column_name,
            observed_wind_column_name,
            simulated_dir_column_name,
            observed_dir_column_name,
        )
    )
    if simulated_wind_speed is None or observed_wind_speed is None:
        print(f"❌ Failed to extract wind speed data")
        return False

    # 绘制风速对比图
    plot_comparison(
        simulated_wind_speed, observed_wind_speed, merged_df, wrf_suffix, station_type
    )

    # 绘制风矢量图（如果有风向数据）
    plot_wind_vectors(
        simulated_wind_speed,
        observed_wind_speed,
        simulated_wind_dir,
        observed_wind_dir,
        merged_df,
        wrf_suffix,
        station_type,
    )

    # 绘制风向对比图（如果有风向数据）
    plot_wind_direction_comparison(
        simulated_wind_dir,
        observed_wind_dir,
        merged_df,
        wrf_suffix,
        station_type,
    )

    # 计算指标（包括风速和风向）
    (
        correlation,
        rmse,
        mae,
        mb,
        dir_mae,
        dir_mb,
        dir_rmse,
        dir_correlation,
        dir_hit_rates,
    ) = calculate_metrics(
        simulated_wind_speed,
        observed_wind_speed,
        simulated_wind_dir,
        observed_wind_dir,
    )

    if correlation is not None:
        # 输出结果
        print(f"\n📊 Analysis Results:")
        print(f"  WIND SPEED:")
        print(f"    Correlation Coefficient (R): {correlation:.4f}")
        print(f"    Root Mean Square Error (RMSE): {rmse:.4f} m/s")
        print(f"    Mean Absolute Error (MAE): {mae:.4f} m/s")
        print(f"    Mean Bias (MB): {mb:.4f} m/s")

        if dir_mae is not None:
            print(f"  WIND DIRECTION:")
            print(f"    Circular Correlation: {dir_correlation:.4f}")
            print(f"    Root Mean Square Error (RMSE): {dir_rmse:.2f}°")
            print(f"    Mean Absolute Error (MAE): {dir_mae:.2f}°")
            print(f"    Mean Bias (MB): {dir_mb:.2f}°")

            if dir_hit_rates:
                print(f"  WIND DIRECTION HIT RATES:")
                for threshold in [10, 22.5, 45]:
                    if threshold in dir_hit_rates:
                        label = ""
                        if threshold == 22.5:
                            label = " (16-direction)"
                        elif threshold == 45:
                            label = " (8-direction)"
                        print(
                            f"    ±{threshold}°{label}: {dir_hit_rates[threshold]:.1f}%"
                        )
                if "class_16" in dir_hit_rates:
                    print(
                        f"    16-direction class accuracy: {dir_hit_rates['class_16']:.1f}%"
                    )
                if "class_8" in dir_hit_rates:
                    print(
                        f"    8-direction class accuracy: {dir_hit_rates['class_8']:.1f}%"
                    )

        # 保存结果
        save_results(
            correlation,
            rmse,
            mae,
            mb,
            wrf_suffix,
            station_type,
            dir_mae,
            dir_mb,
            dir_rmse,
            dir_correlation,
            dir_hit_rates,  # 新增命中率参数
        )

        print(f"✅ Analysis completed successfully!")
        return True
    else:
        print("❌ Could not calculate metrics due to invalid data")
        return False


def create_summary_table(results_dict):
    """
    创建统计指标汇总表格（包括风速、风向和命中率）
    results_dict格式: {(wrf_suffix, station_type):
                      (correlation, rmse, mae, mb, dir_mae, dir_mb, dir_rmse, dir_correlation, dir_hit_rates)}
    """
    print("\n📊 Creating summary statistics table...")

    # 准备数据结构
    summary_data = {}

    # 为每个WRF文件夹创建行数据
    for wrf_suffix in WRF_SUFFIXES:
        summary_data[f"WRF_OUT{wrf_suffix}"] = {}

        for station_type in STATION_TYPES:
            key = (wrf_suffix, station_type)
            if key in results_dict:
                result = results_dict[key]
                # 解包结果（现在包含9个值）
                if len(result) == 9:
                    (
                        correlation,
                        rmse,
                        mae,
                        mb,
                        dir_mae,
                        dir_mb,
                        dir_rmse,
                        dir_correlation,
                        dir_hit_rates,
                    ) = result
                elif len(result) == 8:
                    # 兼容8个值版本（没有命中率）
                    (
                        correlation,
                        rmse,
                        mae,
                        mb,
                        dir_mae,
                        dir_mb,
                        dir_rmse,
                        dir_correlation,
                    ) = result
                    dir_hit_rates = {}
                else:
                    # 兼容旧版本（只有4个值）
                    correlation, rmse, mae, mb = result[:4]
                    dir_mae = dir_mb = dir_rmse = dir_correlation = np.nan
                    dir_hit_rates = {}

                prefix = f"{station_type}_"
                # 风速指标
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_R"] = correlation
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_RMSE"] = rmse
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_MAE"] = mae
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_MB"] = mb
                # 风向指标
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_R"] = dir_correlation
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_RMSE"] = dir_rmse
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_MAE"] = dir_mae
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_MB"] = dir_mb
                # 添加命中率指标（如果有的话）
                if dir_hit_rates and isinstance(dir_hit_rates, dict):
                    # 22.5度命中率（16分位）
                    if 22.5 in dir_hit_rates:
                        summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_HIT22"] = (
                            dir_hit_rates[22.5]
                        )
                    else:
                        summary_data[f"WRF_OUT{wrf_suffix}"][
                            f"{prefix}WD_HIT22"
                        ] = np.nan

                    # 45度命中率（8分位）
                    if 45 in dir_hit_rates:
                        summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_HIT45"] = (
                            dir_hit_rates[45]
                        )
                    else:
                        summary_data[f"WRF_OUT{wrf_suffix}"][
                            f"{prefix}WD_HIT45"
                        ] = np.nan

                    # 8方位准确率（保留但不强调）
                    if "class_8" in dir_hit_rates:
                        summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_CLS8"] = (
                            dir_hit_rates["class_8"]
                        )
                    else:
                        summary_data[f"WRF_OUT{wrf_suffix}"][
                            f"{prefix}WD_CLS8"
                        ] = np.nan
                else:
                    summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_HIT22"] = np.nan
                    summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_HIT45"] = np.nan
                    summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_CLS8"] = np.nan
            else:
                # 如果没有数据，填入NaN
                prefix = f"{station_type}_"
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_R"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_RMSE"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_MAE"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WS_MB"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_R"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_RMSE"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_MAE"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_MB"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_HIT22"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_HIT45"] = np.nan
                summary_data[f"WRF_OUT{wrf_suffix}"][f"{prefix}WD_CLS8"] = np.nan

    # 转换为DataFrame
    df_summary = pd.DataFrame.from_dict(summary_data, orient="index")

    # 重新排列列顺序，按站点类型分组
    columns_order = []
    for station_type in STATION_TYPES:
        # 先风速，后风向
        columns_order.extend(
            [
                f"{station_type}_WS_R",
                f"{station_type}_WS_RMSE",
                f"{station_type}_WS_MAE",
                f"{station_type}_WS_MB",
                f"{station_type}_WD_R",
                f"{station_type}_WD_RMSE",
                f"{station_type}_WD_MAE",
                f"{station_type}_WD_MB",
                f"{station_type}_WD_HIT22",  # 22.5度命中率（16分位）
                f"{station_type}_WD_HIT45",  # 45度命中率（8分位）
                f"{station_type}_WD_CLS8",  # 8方位分类准确率
            ]
        )

    df_summary = df_summary[columns_order]

    # 保存为CSV
    summary_csv_file = os.path.join(
        os.path.dirname(__file__), f"batch_analysis_summary_table_d{DOM_ID:02}.csv"
    )
    df_summary.to_csv(summary_csv_file, float_format="%.4f")
    print(f"📄 Summary table (CSV) saved to: {summary_csv_file}")

    # 保存为格式化的文本表格
    summary_txt_file = os.path.join(
        os.path.dirname(__file__), "batch_analysis_summary_table.txt"
    )
    with open(summary_txt_file, "w", encoding="utf-8") as f:
        f.write("Wind Speed and Direction Analysis Summary Table\n")
        f.write("=" * 120 + "\n\n")

        # 写入表头
        f.write(f"{'WRF_Folder':<12}")
        for station_type in STATION_TYPES:
            f.write(f"  {station_type} Wind Speed (WS)".center(40))
            f.write(
                f"  {station_type} Wind Direction (WD)".center(
                    72
                )  # 增加宽度以容纳HIT45列
            )
        f.write("\n")

        f.write(f"{'':12}")
        for station_type in STATION_TYPES:
            f.write("     R    RMSE   MAE    MB  ")
            f.write("     R    RMSE   MAE    MB   HIT22  HIT45  CLS8")
        f.write("\n")
        f.write("-" * 120 + "\n")

        # 写入数据行
        for wrf_folder in df_summary.index:
            f.write(f"{wrf_folder:<12}")
            for station_type in STATION_TYPES:
                # 风速指标
                ws_r = df_summary.loc[wrf_folder, f"{station_type}_WS_R"]
                ws_rmse = df_summary.loc[wrf_folder, f"{station_type}_WS_RMSE"]
                ws_mae = df_summary.loc[wrf_folder, f"{station_type}_WS_MAE"]
                ws_mb = df_summary.loc[wrf_folder, f"{station_type}_WS_MB"]

                if pd.notna(ws_r):
                    f.write(f" {ws_r:6.3f} {ws_rmse:6.2f} {ws_mae:6.2f} {ws_mb:6.2f}  ")
                else:
                    f.write(f" {'N/A':>6} {'N/A':>6} {'N/A':>6} {'N/A':>6}  ")

                # 风向指标
                wd_r = df_summary.loc[wrf_folder, f"{station_type}_WD_R"]
                wd_rmse = df_summary.loc[wrf_folder, f"{station_type}_WD_RMSE"]
                wd_mae = df_summary.loc[wrf_folder, f"{station_type}_WD_MAE"]
                wd_mb = df_summary.loc[wrf_folder, f"{station_type}_WD_MB"]
                wd_hit22 = df_summary.loc[wrf_folder, f"{station_type}_WD_HIT22"]

                # 获取45度命中率（8分位命中率）
                wd_hit45 = np.nan
                if f"{station_type}_WD_HIT45" in df_summary.columns:
                    wd_hit45 = df_summary.loc[wrf_folder, f"{station_type}_WD_HIT45"]

                # 获取8方位分类准确率
                wd_cls8 = np.nan
                if f"{station_type}_WD_CLS8" in df_summary.columns:
                    wd_cls8 = df_summary.loc[wrf_folder, f"{station_type}_WD_CLS8"]

                if pd.notna(wd_r):
                    # 构建输出字符串
                    output_str = (
                        f" {wd_r:6.3f} {wd_rmse:6.1f} {wd_mae:6.1f} {wd_mb:6.1f}"
                    )

                    # 添加HIT22
                    if pd.notna(wd_hit22):
                        output_str += f" {wd_hit22:6.1f}"
                    else:
                        output_str += f" {'N/A':>6}"

                    # 添加HIT45（8分位命中率）
                    if pd.notna(wd_hit45):
                        output_str += f" {wd_hit45:6.1f}"
                    else:
                        output_str += f" {'N/A':>6}"

                    # 添加CLS8
                    if pd.notna(wd_cls8):
                        output_str += f" {wd_cls8:5.1f}"
                    else:
                        output_str += f" {'N/A':>5}"

                    f.write(output_str)
                else:
                    f.write(
                        f" {'N/A':>6} {'N/A':>6} {'N/A':>6} {'N/A':>6} {'N/A':>6} {'N/A':>6} {'N/A':>5}"
                    )
            f.write("\n")

        f.write("\n\nLegend:\n")
        f.write("WS = Wind Speed, WD = Wind Direction\n")
        f.write("R    = Correlation Coefficient (WS: Pearson, WD: Circular)\n")
        f.write("RMSE = Root Mean Square Error (WS: m/s, WD: degrees)\n")
        f.write("MAE  = Mean Absolute Error (WS: m/s, WD: degrees)\n")
        f.write("MB   = Mean Bias (WS: m/s, WD: degrees)\n")
        f.write("HIT22= Wind Direction Hit Rate within ±22.5° (%, 16-direction)\n")
        f.write("HIT45= Wind Direction Hit Rate within ±45° (%, 8-direction)\n")
        f.write("CLS8 = 8-direction Classification Accuracy (%)\n")
        f.write("30M  = 30M tower station\n")
        f.write("75M  = 75M tower station\n")

    print(f"📄 Summary table (TXT) saved to: {summary_txt_file}")

    # 显示表格预览
    print("\n📋 Summary Table Preview:")
    print(df_summary.head(10).to_string(float_format="{:.4f}".format))

    return df_summary


def plot_combined_comparison(results_data, wrf_suffix):
    """
    绘制两个站点的综合对比图：一张风速对比图和一张风矢量对比图
    两个站点的结果显示在同一个子图中
    """
    print("Generating combined comparison plots for both stations...")
    sys.stdout.flush()

    # 检查是否有两个站点的数据
    if len(results_data) < 2:
        print("Warning: Need data from both stations for combined plots")
        return

    # 关闭之前可能打开的图形，防止内存泄漏
    import matplotlib.pyplot as plt

    plt.close("all")

    # 预处理所有数据，确保都是numpy数组格式，但保持时间数据为datetime
    processed_data = {}
    for station_type, data in results_data.items():
        processed_station = {}
        for key, value in data.items():
            if key == "time_data":
                # 时间数据保持pandas datetime格式，不转换为numpy数组
                processed_station[key] = value
            elif hasattr(value, "values"):
                processed_station[key] = value.values
            elif hasattr(value, "to_numpy"):
                processed_station[key] = value.to_numpy()
            else:
                processed_station[key] = value
        processed_data[station_type] = processed_station

    # 创建输出文件路径
    wrf_folder = f"{WRF_OUT_BASE}{wrf_suffix}"
    output_dir = os.path.join(wrf_folder, "csv")  # 重命名避免与数据变量冲突

    # 确保路径是字符串类型
    wrf_folder = str(wrf_folder)
    output_dir = str(output_dir)

    print(f"Debug: wrf_folder = {wrf_folder}, type: {type(wrf_folder)}")
    print(f"Debug: output_dir = {output_dir}, type: {type(output_dir)}")

    # 1. 绘制综合风速对比图 - 两个站点在同一个图中
    fig1, ax1 = plt.subplots(1, 1, figsize=(15, 8))

    colors = ["blue", "green"]  # 为两个站点使用不同颜色
    line_styles = ["-", "--"]  # 为模拟和观测使用不同线型

    for i, (station_type, data) in enumerate(processed_data.items()):
        sim_speed, obs_speed, time_data = (
            data["sim_speed"],
            data["obs_speed"],
            data["time_data"],
        )
        color = colors[i]

        ax1.plot(
            time_data,
            sim_speed,
            label=f"{station_type} Simulated",
            linewidth=1.5,
            alpha=0.8,
            color=color,
            linestyle="-",
        )
        ax1.plot(
            time_data,
            obs_speed,
            label=f"{station_type} Observed",
            linewidth=1.5,
            alpha=0.8,
            color=color,
            linestyle="--",
        )

    ax1.set_ylabel("Wind Speed (m/s)")
    ax1.set_xlabel("Time")
    ax1.set_title("Combined Wind Speed Comparison - Both Stations")
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 设置时间格式显示 - 使用第一个站点的时间数据作为参考
    try:
        import matplotlib.dates as mdates

        ax1.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
        ax1.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        ax1.tick_params(axis="x", rotation=0)
    except Exception as e:
        print(f"Warning: Could not set time formatting for wind speed plot: {e}")

    plt.tight_layout()

    # 保存风速对比图
    try:
        # 使用重命名后的output_dir变量
        wind_speed_output = os.path.join(
            output_dir, f"combined_wind_speed_comparison_d{DOM_ID:02}.png"
        )
        print(f"Debug: Attempting to save wind speed plot to: {wind_speed_output}")
        print(f"Debug: output_dir = {output_dir}, type: {type(output_dir)}")
        print(f"Debug: wind_speed_output type: {type(wind_speed_output)}")
        plt.savefig(wind_speed_output, dpi=300, bbox_inches="tight")
        print(f"Combined wind speed plot saved to '{wind_speed_output}'")
    except Exception as e:
        print(f"Error saving wind speed plot: {e}")
        print(f"Error type: {type(e)}")
        print(
            f"Debug: output_dir value and type: {repr(output_dir)}, {type(output_dir)}"
        )
        import traceback

        traceback.print_exc()

    plt.close()

    # 2. 绘制综合风矢量对比图 - 两个站点在同一个图中
    fig2, ax2 = plt.subplots(1, 1, figsize=(15, 10))

    station_colors = {
        "30M": ["blue", "red"],
        "75M": ["darkblue", "darkred"],
    }  # 每个站点的模拟和观测颜色
    y_positions = {"30M": [0.3, 0.7], "75M": [1.3, 1.7]}  # 每个站点的Y位置

    for station_type, data in processed_data.items():
        if data["sim_dir"] is None or data["obs_dir"] is None:
            print(f"Warning: Wind direction data not available for {station_type}")
            continue

        sim_speed, obs_speed = data["sim_speed"], data["obs_speed"]
        sim_dir, obs_dir = data["sim_dir"], data["obs_dir"]
        time_data = data["time_data"]

        # 转换为数值类型并清理数据（数据已预处理，但仍需数值转换）
        sim_speed = pd.to_numeric(sim_speed, errors="coerce")
        obs_speed = pd.to_numeric(obs_speed, errors="coerce")
        sim_dir = pd.to_numeric(sim_dir, errors="coerce")
        obs_dir = pd.to_numeric(obs_dir, errors="coerce")

        # 转换风向为弧度（气象风向转数学风向）
        sim_dir_rad = np.radians(90 - sim_dir)
        obs_dir_rad = np.radians(90 - obs_dir)

        # 计算风矢量分量
        sim_u = sim_speed * np.cos(sim_dir_rad)
        sim_v = sim_speed * np.sin(sim_dir_rad)
        obs_u = obs_speed * np.cos(obs_dir_rad)
        obs_v = obs_speed * np.sin(obs_dir_rad)

        # 为了清晰显示，每隔N个数据点绘制一个箭头
        step = max(1, len(time_data) // 30)
        indices = np.arange(0, len(time_data), step)

        sim_u_subset = sim_u[indices]
        sim_v_subset = sim_v[indices]
        obs_u_subset = obs_u[indices]
        obs_v_subset = obs_v[indices]

        # 使用时间作为X坐标
        time_subset = time_data.iloc[indices]

        # 转换为数值化的时间用于箭头绘制
        import matplotlib.dates as mdates

        x_positions = mdates.date2num(time_subset)

        print(f"Debug: Using {len(x_positions)} arrow positions for {station_type}")

        # 为每个站点设置不同的Y位置
        y_sim = np.ones(len(x_positions)) * y_positions[station_type][1]  # 模拟数据位置
        y_obs = np.ones(len(x_positions)) * y_positions[station_type][0]  # 观测数据位置

        colors = station_colors[station_type]

        # 绘制模拟风矢量
        valid_sim = ~(np.isnan(sim_u_subset) | np.isnan(sim_v_subset))
        if np.any(valid_sim):
            ax2.quiver(
                x_positions[valid_sim],
                y_sim[valid_sim],
                sim_u_subset[valid_sim],
                sim_v_subset[valid_sim],
                scale=100,
                scale_units="width",
                width=0.003,
                color=colors[0],
                alpha=0.8,
            )

        # 绘制观测风矢量
        valid_obs = ~(np.isnan(obs_u_subset) | np.isnan(obs_v_subset))
        if np.any(valid_obs):
            ax2.quiver(
                x_positions[valid_obs],
                y_obs[valid_obs],
                obs_u_subset[valid_obs],
                obs_v_subset[valid_obs],
                scale=100,
                scale_units="width",
                width=0.003,
                color=colors[1],
                alpha=0.8,
            )

    ax2.set_ylabel("Wind Vectors (Both Stations)")
    ax2.set_xlabel("Time")
    ax2.set_title("Combined Wind Vector Comparison - Both Stations")
    ax2.set_ylim(0, 2.2)
    ax2.grid(True, alpha=0.3)

    # 设置Y轴标签 - 显示四个层级
    ax2.set_yticks([0.3, 0.7, 1.3, 1.7])
    ax2.set_yticklabels(["30M Obs", "30M Sim", "75M Obs", "75M Sim"], rotation=90)

    # 添加图例
    legend_elements = []
    for station_type in processed_data.keys():
        colors = station_colors[station_type]
        legend_elements.append(
            plt.Line2D(
                [0],
                [0],
                marker=">",
                markersize=8,
                color=colors[0],
                linestyle="None",
                label=f"{station_type} Simulated",
            )
        )
        legend_elements.append(
            plt.Line2D(
                [0],
                [0],
                marker=">",
                markersize=8,
                color=colors[1],
                linestyle="None",
                label=f"{station_type} Observed",
            )
        )

    ax2.legend(handles=legend_elements, loc="upper right")

    # 设置时间格式显示
    try:
        ax2.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d-%H"))
        ax2.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        ax2.tick_params(axis="x", rotation=0)
    except Exception as e:
        print(f"Warning: Could not set time formatting for wind vector plot: {e}")

    plt.tight_layout()

    # 保存风矢量对比图
    try:
        # 使用重命名后的output_dir变量
        wind_vector_output = os.path.join(
            output_dir, f"combined_wind_vector_comparison_d{DOM_ID:02}.png"
        )
        print(f"Debug: Attempting to save wind vector plot to: {wind_vector_output}")
        print(f"Debug: output_dir = {output_dir}, type: {type(output_dir)}")
        print(f"Debug: wind_vector_output type: {type(wind_vector_output)}")
        plt.savefig(wind_vector_output, dpi=300, bbox_inches="tight")
        print(f"Combined wind vector plot saved to '{wind_vector_output}'")
    except Exception as e:
        print(f"Error saving wind vector plot: {e}")
        print(f"Error type: {type(e)}")
        print(
            f"Debug: output_dir value and type: {repr(output_dir)}, {type(output_dir)}"
        )
        import traceback

        traceback.print_exc()

    plt.close()  # 确保关闭当前图形

    # 最终清理，确保所有图形都被关闭
    plt.close("all")


def main():
    """
    主函数 - 批处理所有WRF输出文件夹和站点类型
    """
    print("Starting batch wind speed analysis...")
    print(f"WRF output folders: {WRF_OUT_BASE}2 to {WRF_OUT_BASE}11")
    print(f"Station types: {STATION_TYPES}")
    print(f"Domain ID: d{DOM_ID:02}")

    # 统计结果
    total_cases = len(WRF_SUFFIXES) * len(STATION_TYPES)
    successful_cases = 0
    failed_cases = []
    results_dict = {}  # 存储所有成功的分析结果
    combined_plot_data = {}  # 存储用于综合对比图的数据

    start_time = pd.Timestamp.now()

    # 批处理所有组合
    for wrf_suffix in WRF_SUFFIXES:
        print(f"\n{'='*80}")
        print(f"Processing WRF_OUT{wrf_suffix}")
        print(f"{'='*80}")

        # 为每个WRF输出文件夹收集两个站点的数据
        wrf_station_data = {}

        for station_type in STATION_TYPES:
            try:
                print(f"\n{'='*60}")
                print(f"Processing WRF_OUT{wrf_suffix} - {station_type}")
                print(f"{'='*60}")

                # 获取文件路径和配置
                simulated_file, observed_file = get_file_paths(wrf_suffix, station_type)
                config = get_wind_column_config(station_type)
                simulated_wind_column_name, observed_wind_column_name = (
                    config[0],
                    config[1],
                )
                simulated_dir_column_name, observed_dir_column_name = config[2], (
                    config[3] if len(config) >= 4 else (None, None)
                )

                print(f"Configuration:")
                print(f"  WRF output folder: {WRF_OUT_BASE}{wrf_suffix}")
                print(f"  Domain ID: d{DOM_ID:02}")
                print(f"  Station type: {station_type}")
                print(f"  Simulated file: {simulated_file}")
                print(f"  Observed file: {observed_file}")
                print(f"  Simulated wind column: {simulated_wind_column_name}")
                print(f"  Observed wind column: {observed_wind_column_name}")
                print(f"  Simulated wind direction column: {simulated_dir_column_name}")
                print(f"  Observed wind direction column: {observed_dir_column_name}")

                # 检查文件是否存在
                if not os.path.exists(simulated_file):
                    print(f"❌ Simulated file not found: {simulated_file}")
                    failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")
                    continue
                if not os.path.exists(observed_file):
                    print(f"❌ Observed file not found: {observed_file}")
                    failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")
                    continue

                # 读取数据
                sim_df, obs_df = read_and_validate_data(simulated_file, observed_file)
                if sim_df is None or obs_df is None:
                    print(f"❌ Failed to read data files")
                    failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")
                    continue

                # 通过时间列对齐数据
                merged_df = align_data_by_time(sim_df, obs_df)
                if merged_df is None or len(merged_df) == 0:
                    print(f"❌ Failed to align data or no matching time points")
                    failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")
                    continue

                # 提取风速和风向数据
                (
                    simulated_wind_speed,
                    observed_wind_speed,
                    simulated_wind_dir,
                    observed_wind_dir,
                ) = extract_wind_data(
                    merged_df,
                    simulated_wind_column_name,
                    observed_wind_column_name,
                    simulated_dir_column_name,
                    observed_dir_column_name,
                )
                if simulated_wind_speed is None or observed_wind_speed is None:
                    print(f"❌ Failed to extract wind speed data")
                    failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")
                    continue

                # 绘制风速对比图
                plot_comparison(
                    simulated_wind_speed,
                    observed_wind_speed,
                    merged_df,
                    wrf_suffix,
                    station_type,
                )

                # 绘制风矢量图（如果有风向数据）
                plot_wind_vectors(
                    simulated_wind_speed,
                    observed_wind_speed,
                    simulated_wind_dir,
                    observed_wind_dir,
                    merged_df,
                    wrf_suffix,
                    station_type,
                )

                # 绘制风向对比图（如果有风向数据）
                plot_wind_direction_comparison(
                    simulated_wind_dir,
                    observed_wind_dir,
                    merged_df,
                    wrf_suffix,
                    station_type,
                )

                # 计算指标（包括风速和风向）
                (
                    correlation,
                    rmse,
                    mae,
                    mb,
                    dir_mae,
                    dir_mb,
                    dir_rmse,
                    dir_correlation,
                    dir_hit_rates,  # 新增命中率
                ) = calculate_metrics(
                    simulated_wind_speed,
                    observed_wind_speed,
                    simulated_wind_dir,
                    observed_wind_dir,
                )

                if correlation is not None:
                    # 输出结果
                    print(f"\n📊 Analysis Results:")
                    print(f"  WIND SPEED:")
                    print(f"    Correlation Coefficient (R): {correlation:.4f}")
                    print(f"    Root Mean Square Error (RMSE): {rmse:.4f} m/s")
                    print(f"    Mean Absolute Error (MAE): {mae:.4f} m/s")
                    print(f"    Mean Bias (MB): {mb:.4f} m/s")

                    if dir_mae is not None:
                        print(f"  WIND DIRECTION:")
                        print(f"    Circular Correlation: {dir_correlation:.4f}")
                        print(f"    Root Mean Square Error (RMSE): {dir_rmse:.2f}°")
                        print(f"    Mean Absolute Error (MAE): {dir_mae:.2f}°")
                        print(f"    Mean Bias (MB): {dir_mb:.2f}°")

                        if dir_hit_rates:
                            print(f"  WIND DIRECTION HIT RATES:")
                            for threshold in [10, 22.5, 45]:
                                if threshold in dir_hit_rates:
                                    print(
                                        f"    ±{threshold}°: {dir_hit_rates[threshold]:.1f}%"
                                    )
                            if "class_16" in dir_hit_rates:
                                print(
                                    f"    16-direction accuracy: {dir_hit_rates['class_16']:.1f}%"
                                )
                            if "class_8" in dir_hit_rates:
                                print(
                                    f"    8-direction accuracy: {dir_hit_rates['class_8']:.1f}%"
                                )

                    # 保存结果
                    save_results(
                        correlation,
                        rmse,
                        mae,
                        mb,
                        wrf_suffix,
                        station_type,
                        dir_mae,
                        dir_mb,
                        dir_rmse,
                        dir_correlation,
                        dir_hit_rates,  # 新增命中率参数
                    )

                    # 存储结果到字典中用于汇总表格（现在包含9个值）
                    results_dict[(wrf_suffix, station_type)] = (
                        correlation,
                        rmse,
                        mae,
                        mb,
                        dir_mae if dir_mae is not None else np.nan,
                        dir_mb if dir_mb is not None else np.nan,
                        dir_rmse if dir_rmse is not None else np.nan,
                        dir_correlation if dir_correlation is not None else np.nan,
                        (
                            dir_hit_rates if dir_hit_rates is not None else {}
                        ),  # 命中率字典
                    )

                    # 收集数据用于综合对比图
                    wrf_station_data[station_type] = {
                        "sim_speed": simulated_wind_speed,
                        "obs_speed": observed_wind_speed,
                        "sim_dir": simulated_wind_dir,
                        "obs_dir": observed_wind_dir,
                        "time_data": merged_df[SIMULATED_TIME_COLUMN],
                    }

                    print(f"✅ Analysis completed successfully!")
                    successful_cases += 1
                else:
                    print("❌ Could not calculate metrics due to invalid data")
                    failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")

            except Exception as e:
                print(
                    f"❌ Error processing WRF_OUT{wrf_suffix}-{station_type}: {str(e)}"
                )
                failed_cases.append(f"WRF_OUT{wrf_suffix}-{station_type}")

        # 生成每个WRF文件夹的综合对比图（如果有两个站点的数据）
        if len(wrf_station_data) == 2:
            try:
                print(f"\n📊 Generating combined plots for WRF_OUT{wrf_suffix}...")
                print(f"Debug: Station data keys: {list(wrf_station_data.keys())}")
                for station, data in wrf_station_data.items():
                    print(f"Debug: {station} data keys: {list(data.keys())}")
                    for key, value in data.items():
                        print(
                            f"Debug: {station}.{key} type: {type(value)}, shape: {getattr(value, 'shape', 'N/A')}"
                        )

                plot_combined_comparison(wrf_station_data, wrf_suffix)
            except Exception as e:
                print(
                    f"❌ Error generating combined plots for WRF_OUT{wrf_suffix}: {str(e)}"
                )
                print(f"Error type: {type(e)}")
                import traceback

                traceback.print_exc()

    # 最终统计报告
    end_time = pd.Timestamp.now()
    duration = end_time - start_time

    print(f"\n{'='*60}")
    print(f"📋 BATCH PROCESSING SUMMARY")
    print(f"{'='*60}")
    print(f"Total cases: {total_cases}")
    print(f"Successful: {successful_cases}")
    print(f"Failed: {len(failed_cases)}")
    print(f"Success rate: {successful_cases/total_cases*100:.1f}%")
    print(f"Processing time: {duration}")

    if failed_cases:
        print(f"\n❌ Failed cases:")
        for case in failed_cases:
            print(f"  - {case}")

    print(f"\n✅ Batch processing completed!")

    # 创建汇总统计表格
    if results_dict:
        df_summary = create_summary_table(results_dict)

    # 创建汇总文件
    summary_file = os.path.join(os.path.dirname(__file__), "batch_analysis_summary.txt")
    with open(summary_file, "w", encoding="utf-8") as f:
        f.write("Batch Wind Speed Analysis Summary\\n")
        f.write("=" * 40 + "\\n")
        f.write(f"Processing time: {start_time} to {end_time}\\n")
        f.write(f"Total duration: {duration}\\n")
        f.write(f"Total cases: {total_cases}\\n")
        f.write(f"Successful: {successful_cases}\\n")
        f.write(f"Failed: {len(failed_cases)}\\n")
        f.write(f"Success rate: {successful_cases/total_cases*100:.1f}%\\n")
        if failed_cases:
            f.write("\\nFailed cases:\\n")
            for case in failed_cases:
                f.write(f"  - {case}\\n")

    print(f"📄 Summary saved to: {summary_file}")


if __name__ == "__main__":
    main()
