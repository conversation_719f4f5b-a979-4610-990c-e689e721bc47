# -*- coding: utf-8 -*-
import ee
import geemap
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from datetime import datetime
import calendar
from pathlib import Path

ee.Authenticate()
# 初始化 GEE 项目
ee.Initialize(project="ee-mikeyrule1")

# 统一输出目录
OUT_DIR = Path(r"E:/output")
OUT_DIR.mkdir(parents=True, exist_ok=True)

# 研究区（山西）的区域ID
roi = ee.FeatureCollection("users/mikeyrule1/bianjie")

# 设置地图视图
Map = geemap.Map()
Map.centerObject(roi, 10)
Map.setOptions("SATELLITE")
Map.addLayer(roi.style(color="black", fillColor="ffffff00"), {}, "ROI")

# ===========1) 参数 ===========
dataset ='ECMWF/ERA5_LAND/HOURLY'
var='temperature_2m'      # Kelvin
start_date ='2015-01-01'
end_date   ='2016-12-31'
scale_m =10_000            # 10 km 左右，与 ERA5-Land 分辨率接近

# ===========2) 按月提取 ROI平均每小时气温(℃)===========
def month_range(year):
    for m in range(1,13):
        start = datetime(year, m, 1)
        end = datetime(year + (m==12), (m % 12) + 1, 1)
        yield (start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d'), year, m)

def ic_to_hourly_df(start, end, roi, scale=scale_m):
    """把 [start, end) 的小时序列转成 DataFrame（UTC→北京时）"""
    ic = (ee.ImageCollection(dataset)
          .filterDate(start, end)
          .filterBounds(roi)
          .select([var]))

    # 每幅影像 → 该 ROI 的平均温度（℃）
    def img_to_feat(img):
        temp_c_img = img.select(var).subtract(273.15).rename('temp_c')
        mean_val = temp_c_img.reduceRegion(
            reducer=ee.Reducer.mean(),
            geometry=roi, scale=scale, maxPixels=1e13, bestEffort=True
        ).get('temp_c')
        return ee.Feature(None,{'temp_c': mean_val,'time': img.date().millis()})

    fc = ic.map(img_to_feat)

    # 转 pandas（优先 geemap.ee_to_pandas，失败再 getInfo 兜底）
    try:
        df = geemap.ee_to_pandas(fc)
    except Exception:
        feats = fc.getInfo().get('features',[])
        df = pd.DataFrame([{'temp_c': f['properties']['temp_c'],
                           'time': f['properties']['time']} for f in feats])

    if df.empty:
        return df

    # 时间转北京时，并拆出年月日小时
    # 先转换为datetime，再处理时区
    df['datetime'] = pd.to_datetime(df['time'], unit='ms')
    # 假设原始时间是UTC，转换为北京时间（UTC+8）
    df['datetime'] = df['datetime'] + pd.Timedelta(hours=8)
    df['year'] = df['datetime'].dt.year
    df['month'] = df['datetime'].dt.month
    df['day'] = df['datetime'].dt.day
    df['hour'] = df['datetime'].dt.hour
    return df[['datetime','year','month','day','hour','temp_c']].sort_values('datetime')

# 汇总 2015–2016
dfs = []
for y in [2015,2016]:
    for m_start, m_end, yy, mm in month_range(y):
        print(f'Pulling {yy}-{mm:02d} ...')
        d = ic_to_hourly_df(m_start, m_end, roi, scale=scale_m)
        if not d.empty:
            dfs.append(d)

df = pd.concat(dfs, ignore_index=True).dropna(subset=['temp_c'])
print('Total hourly records:',len(df))

# 可选：保存原始小时表
csv_out = OUT_DIR/'jinzhong_era5land_hourly_T2m_2015_2016.csv'
df.to_csv(csv_out, index=False)

# ===========3) 画图 ===========
years  = [2015,2016]
months = list(range(1,13))

# 色标范围（剔除极端）
vmin = df['temp_c'].quantile(0.01)
vmax = df['temp_c'].quantile(0.99)

nrows, ncols = len(years), 12
fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(18,9), constrained_layout=False)
if nrows == 1:
    axes = np.array([axes])

for i, yy in enumerate(years):
    for j, mm in enumerate(months):
        ax = axes[i, j]
        sub = df[(df['year']==yy)&(df['month']==mm)].copy()
        if sub.empty:
            ax.axis('off')
            continue

        ndays = calendar.monthrange(yy, mm)[1]
        pivot = (sub.pivot_table(index='hour', columns='day', values='temp_c', aggfunc='mean')
                 .reindex(index=range(0,24), columns=range(1, ndays+1)))

        from matplotlib.colors import TwoSlopeNorm
        CMAP='RdYlBu_r'                       # 可换 'coolwarm'/'plasma'/'viridis'
        norm = TwoSlopeNorm(vcenter=0, vmin=vmin, vmax=vmax)

        im = ax.imshow(pivot.values[::-1], aspect='auto', interpolation='nearest',
                       norm=norm, cmap=CMAP, extent=[1, ndays+1,0,24])

        # 轴样式：左边显示小时，底部显示日期刻度
        if j == 0:
            ax.set_ylabel('Hour Commencing', fontsize=8)
            ax.set_yticks([1,6,12,18,23])
            ax.set_yticklabels([23,18,12,6,1][::-1])
        else:
            ax.set_yticks([])

        if i == nrows-1:
            ticks = [x for x in [1,10,20,31] if x <= ndays]
            ax.set_xticks(ticks)
            ax.set_xlabel('Day', fontsize=8)
        else:
            ax.set_xticks([])

        ax.set_title(datetime(yy, mm, 1).strftime('%b'), fontsize=10)

    # 每行右侧标注年份
    axes[i,-1].text(1.06, 0.5, f'{yy}', transform=axes[i,-1].transAxes,
                     rotation=270, va='center', ha='left', fontsize=10)

# 总标题 & 统一色条
fig.suptitle('Hourly Temps (2m) — ERA5-Land — ShanXi, 2015–2016', fontsize=14, y=0.98)
cbar_ax = fig.add_axes([0.25,0.06,0.5,0.02])  # [left,bottom,width,height]
cbar = fig.colorbar(im, cax=cbar_ax, orientation='horizontal')
cbar.set_label('Hrly Temps (°C)')

# 布局细化（上下/左右间距）
plt.subplots_adjust(top=0.92, left=0.06, right=0.97, hspace=0.15, wspace=0.15)
png_out = OUT_DIR/'jinzhong_hourly_temp_heatmap_2015_2016.png'
plt.savefig(png_out, dpi=300, bbox_inches='tight')
plt.show()
print("Done. 输出：CSV 与 PNG 已生成。")