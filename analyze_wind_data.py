import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr
import sys

# 读取CSV文件
file_path = r"F:\项目\Code\csv\wrf_station_data_5min_parallel_d04.csv"
print(f"Reading file: {file_path}")
sys.stdout.flush()

try:
    df = pd.read_csv(file_path)
    print(f"Data shape: {df.shape}")
    print("File read successfully")
    sys.stdout.flush()
except Exception as e:
    print(f"Error reading file: {e}")
    sys.exit(1)

# 提取需要的数据列
# 模拟结果：75M_75m_wind_speed_ms (第14列，索引为13)
# 实际观测结果：WS (m/s) (第17列，索引为16)
print("Extracting columns...")
sys.stdout.flush()

try:
    simulated_wind_speed = df.iloc[:, 10]  # 75M_75m_wind_speed_ms
    observed_wind_speed = df.iloc[:, 17]  # WS (m/s)
    print(f"Simulated wind speed shape: {simulated_wind_speed.shape}")
    print(f"Observed wind speed shape: {observed_wind_speed.shape}")
    sys.stdout.flush()
except Exception as e:
    print(f"Error extracting columns: {e}")
    sys.exit(1)

# 创建时间序列用于绘图
print("Creating time series...")
sys.stdout.flush()
time_points = range(len(simulated_wind_speed))

# 绘制折线图
print("Generating plot...")
sys.stdout.flush()
plt.figure(figsize=(12, 6))
plt.plot(
    time_points,
    simulated_wind_speed,
    label="Simulated Wind Speed (75M_75m)",
    linewidth=1,
)
plt.plot(
    time_points, observed_wind_speed, label="Observed Wind Speed (WS)", linewidth=1
)
plt.xlabel("Time Points (5-min intervals)")
plt.ylabel("Wind Speed (m/s)")
plt.title("Comparison of Simulated vs Observed Wind Speed")
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig("wind_speed_comparison.png", dpi=300, bbox_inches="tight")
# plt.show()  # 避免显示图形界面
print("Plot saved to 'wind_speed_comparison.png'")
sys.stdout.flush()

# 计算相关性指标
print("Calculating metrics...")
sys.stdout.flush()

# 1. 相关性系数 (R)
try:
    correlation, p_value = pearsonr(simulated_wind_speed, observed_wind_speed)
    print(f"Correlation calculated: {correlation:.4f}")
    sys.stdout.flush()
except Exception as e:
    print(f"Error calculating correlation: {e}")
    sys.exit(1)

# 2. 均方根误差 (RMSE)
try:
    rmse = np.sqrt(mean_squared_error(observed_wind_speed, simulated_wind_speed))
    print(f"RMSE calculated: {rmse:.4f}")
    sys.stdout.flush()
except Exception as e:
    print(f"Error calculating RMSE: {e}")
    sys.exit(1)

# 3. 平均绝对误差 (MAE)
try:
    mae = mean_absolute_error(observed_wind_speed, simulated_wind_speed)
    print(f"MAE calculated: {mae:.4f}")
    sys.stdout.flush()
except Exception as e:
    print(f"Error calculating MAE: {e}")
    sys.exit(1)

# 4. 平均偏差 (MB)
try:
    mb = np.mean(simulated_wind_speed - observed_wind_speed)
    print(f"MB calculated: {mb:.4f}")
    sys.stdout.flush()
except Exception as e:
    print(f"Error calculating MB: {e}")
    sys.exit(1)

# 输出结果
print(f"Correlation Coefficient (R): {correlation:.4f}")
print(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s")
print(f"Mean Absolute Error (MAE): {mae:.4f} m/s")
print(f"Mean Bias (MB): {mb:.4f} m/s")

# 保存统计结果到文本文件
print("Saving results to file...")
sys.stdout.flush()
with open("wind_speed_statistics.txt", "w") as f:
    f.write("Wind Speed Analysis Results\n")
    f.write("=" * 30 + "\n")
    f.write(f"Correlation Coefficient (R): {correlation:.4f}\n")
    f.write(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s\n")
    f.write(f"Mean Absolute Error (MAE): {mae:.4f} m/s\n")
    f.write(f"Mean Bias (MB): {mb:.4f} m/s\n")

print(
    "Analysis completed. Results saved to 'wind_speed_statistics.txt' and plot saved to 'wind_speed_comparison.png'"
)
