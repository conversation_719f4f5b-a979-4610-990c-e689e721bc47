import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr

# 读取CSV文件
file_path = r'F:\项目\Code\csv\wrf_station_data_5min_parallel_d04.csv'

# 读取数据，只取前1000行以避免内存问题
print("Reading data...")
df = pd.read_csv(file_path, nrows=1000)
print(f"Data shape: {df.shape}")

# 提取需要的数据列
# 模拟结果：75M_75m_wind_speed_ms (第14列，索引为13)
# 实际观测结果：WS (m/s) (第17列，索引为16)
simulated_wind_speed = df.iloc[:, 13]  # 75M_75m_wind_speed_ms
observed_wind_speed = df.iloc[:, 16]   # WS (m/s)

# 移除任何NaN值
mask = ~(np.isnan(simulated_wind_speed) | np.isnan(observed_wind_speed))
simulated_wind_speed = simulated_wind_speed[mask]
observed_wind_speed = observed_wind_speed[mask]

print(f"Valid data points: {len(simulated_wind_speed)}")

# 创建时间序列用于绘图
time_points = range(len(simulated_wind_speed))

# 绘制折线图
print("Generating plot...")
plt.figure(figsize=(12, 6))
plt.plot(time_points, simulated_wind_speed, label='Simulated Wind Speed (75M_75m)', linewidth=1)
plt.plot(time_points, observed_wind_speed, label='Observed Wind Speed (WS)', linewidth=1)
plt.xlabel('Time Points (5-min intervals)')
plt.ylabel('Wind Speed (m/s)')
plt.title('Comparison of Simulated vs Observed Wind Speed (Sample)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('wind_speed_comparison.png', dpi=300, bbox_inches='tight')
# plt.show()  # 避免显示图形界面

# 计算相关性指标
print("Calculating metrics...")
# 1. 相关性系数 (R)
correlation, p_value = pearsonr(simulated_wind_speed, observed_wind_speed)

# 2. 均方根误差 (RMSE)
rmse = np.sqrt(mean_squared_error(observed_wind_speed, simulated_wind_speed))

# 3. 平均绝对误差 (MAE)
mae = mean_absolute_error(observed_wind_speed, simulated_wind_speed)

# 4. 平均偏差 (MB)
mb = np.mean(simulated_wind_speed - observed_wind_speed)

# 输出结果
print(f"Correlation Coefficient (R): {correlation:.4f}")
print(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s")
print(f"Mean Absolute Error (MAE): {mae:.4f} m/s")
print(f"Mean Bias (MB): {mb:.4f} m/s")

# 保存统计结果到文本文件
with open('wind_speed_statistics.txt', 'w') as f:
    f.write("Wind Speed Analysis Results (Sample)\n")
    f.write("=" * 30 + "\n")
    f.write(f"Correlation Coefficient (R): {correlation:.4f}\n")
    f.write(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s\n")
    f.write(f"Mean Absolute Error (MAE): {mae:.4f} m/s\n")
    f.write(f"Mean Bias (MB): {mb:.4f} m/s\n")

print("Analysis completed. Results saved to 'wind_speed_statistics.txt' and plot saved to 'wind_speed_comparison.png'")