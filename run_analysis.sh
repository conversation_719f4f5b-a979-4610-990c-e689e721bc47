#!/bin/bash
# Linux运行脚本 - run_analysis.sh

echo "======================================"
echo "WRF Wind Analysis - Linux Version"
echo "======================================"

# 检查Python版本
python3 --version

# 检查必要的包
echo "检查依赖包..."
python3 -c "import pandas; print('pandas:', pandas.__version__)"
python3 -c "import numpy; print('numpy:', numpy.__version__)"
python3 -c "import matplotlib; print('matplotlib:', matplotlib.__version__)"

# 设置环境变量（可选）
export MPLBACKEND=Agg  # 无显示器环境

# 运行主程序
echo ""
echo "开始运行分析程序..."
python3 analyze_wind_data_separated.py

# 检查输出文件
echo ""
echo "生成的文件："
ls -la *.csv *.txt *.png 2>/dev/null

echo ""
echo "分析完成！"