import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr

# 读取CSV文件
file_path = r"F:\项目\Code\csv\wrf_station_data_5min_parallel_d04.csv"
print(f"Reading file: {file_path}")

df = pd.read_csv(file_path)
print(f"Data shape: {df.shape}")

# 提取需要的数据列
# 模拟结果：75M_75m_wind_speed_ms (第14列，索引为13)
# 实际观测结果：WS (m/s) (第17列，索引为16)
simulated_wind_speed = df.iloc[:, 13]  # 75M_75m_wind_speed_ms
observed_wind_speed = df.iloc[:, 17]  # WS (m/s)


# 将数据重新整理为30分钟间隔的平均值
# 每6个5分钟数据点组成一个30分钟平均值
def calculate_30min_averages(data):
    # 计算有多少个完整的30分钟区间
    n_intervals = len(data) // 6
    # 只取完整区间的部分
    trimmed_data = data[: n_intervals * 6]
    # 重塑为(n_intervals, 6)的数组，然后计算每行的平均值
    reshaped = trimmed_data.values.reshape(n_intervals, 6)
    return np.mean(reshaped, axis=1)


print("Calculating 30-minute averages...")
simulated_30min = calculate_30min_averages(simulated_wind_speed)
observed_30min = calculate_30min_averages(observed_wind_speed)

print(f"Original data points: {len(simulated_wind_speed)}")
print(f"30-minute averaged data points: {len(simulated_30min)}")

# 创建时间序列用于绘图
time_points = range(len(simulated_30min))

# 绘制折线图
print("Generating plot...")
plt.figure(figsize=(12, 6))
plt.plot(
    time_points,
    simulated_30min,
    label="Simulated Wind Speed (75M_75m) - 30min Avg",
    linewidth=1,
)
plt.plot(
    time_points,
    observed_30min,
    label="Observed Wind Speed (WS) - 30min Avg",
    linewidth=1,
)
plt.xlabel("Time Points (30-min intervals)")
plt.ylabel("Wind Speed (m/s)")
plt.title("Comparison of Simulated vs Observed Wind Speed (30-minute Averages)")
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig("wind_speed_comparison_30min.png", dpi=300, bbox_inches="tight")
# plt.show()  # 避免显示图形界面
print("Plot saved to 'wind_speed_comparison_30min.png'")

# 计算相关性指标
print("Calculating metrics...")
# 1. 相关性系数 (R)
correlation, p_value = pearsonr(simulated_30min, observed_30min)

# 2. 均方根误差 (RMSE)
rmse = np.sqrt(mean_squared_error(observed_30min, simulated_30min))

# 3. 平均绝对误差 (MAE)
mae = mean_absolute_error(observed_30min, simulated_30min)

# 4. 平均偏差 (MB)
mb = np.mean(simulated_30min - observed_30min)

# 输出结果
print(f"Correlation Coefficient (R): {correlation:.4f}")
print(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s")
print(f"Mean Absolute Error (MAE): {mae:.4f} m/s")
print(f"Mean Bias (MB): {mb:.4f} m/s")

# 保存统计结果到文本文件
with open("wind_speed_statistics_30min.txt", "w") as f:
    f.write("Wind Speed Analysis Results (30-minute Averages)\n")
    f.write("=" * 45 + "\n")
    f.write(f"Original Data Points: {len(simulated_wind_speed)}\n")
    f.write(f"30-minute Averaged Data Points: {len(simulated_30min)}\n")
    f.write(f"Correlation Coefficient (R): {correlation:.4f}\n")
    f.write(f"Root Mean Square Error (RMSE): {rmse:.4f} m/s\n")
    f.write(f"Mean Absolute Error (MAE): {mae:.4f} m/s\n")
    f.write(f"Mean Bias (MB): {mb:.4f} m/s\n")

print(
    "Analysis completed. Results saved to 'wind_speed_statistics_30min.txt' and plot saved to 'wind_speed_comparison_30min.png'"
)
