import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from matplotlib import rcParams
from sklearn.metrics import (
    mean_squared_error,
    mean_absolute_error,
    explained_variance_score,
)
from scipy.stats import linregress
from mpl_toolkits.axes_grid1 import make_axes_locatable  # ✅ 新增
from multiprocessing import Pool, cpu_count
import functools
from tqdm import tqdm  # 进度条库
import time

# 设置全局字体
config = {"font.family": "Times New Roman", "font.size": 22, "mathtext.fontset": "stix"}
rcParams.update(config)

# 全局字体大小变量
AXIS_LABEL_SIZE = 30  # 坐标轴标签字体大小
AXIS_TICK_SIZE = 30  # 坐标轴刻度字体大小
TEXT_SIZE = 30  # 文本字体大小
COLORBAR_LABEL_SIZE = 30  # 色标标签字体大小
COLORBAR_TICK_SIZE = 30  # 色标刻度字体大小
SCATTER_SIZE = 30  # 散点大小
COLORBAR_PAD = 0.15  # 色标与主图的距离

# 并行处理参数
N_CORES = min(cpu_count(), 16)  # 限制CPU核心数，避免过多进程
MEMORY_LIMIT = False  # 内存优化开关

# 内存优化参数
MAX_SAMPLE_SIZE = 1000000  # 最大样本数量，平衡质量和内存使用
CHUNK_SIZE = 10000  # 分块处理大小
DENSITY_SAMPLE_SIZE = 50000  # 密度计算专用采样数量（进一步减小）
USE_FAST_DENSITY = True  # 使用快速密度计算方法

# 加载数据并采样
data = pd.read_excel("F:/项目/density_plot_1.xlsx")
data_ = data[(data["cosi_20240428"] < 1) & (data["B8"] < 1)]

# 内存优化：智能采样
if MEMORY_LIMIT and len(data_) > MAX_SAMPLE_SIZE:
    print(
        f"数据量过大 ({len(data_):,} 条)，采样至 {MAX_SAMPLE_SIZE:,} 条以优化内存使用"
    )
    data_ = data_.sample(MAX_SAMPLE_SIZE, random_state=42)
else:
    print(f"处理数据量：{len(data_):,} 条")
# data_ = data_.sample(100000, random_state=42)  # 固定随机种子，便于复现

# 提取变量
x = data_["cosi_20240428"]
y = data_["B8"]


# 并行计算评估指标的辅助函数
def calculate_bias(x, y):
    return np.mean(x - y)


def calculate_mse(x, y):
    return mean_squared_error(x, y)


def calculate_mae(x, y):
    return mean_absolute_error(x, y)


def calculate_ev(x, y):
    return explained_variance_score(x, y)


# 优化的密度计算函数
def calculate_density_optimized(x_data, y_data, sample_size):
    """优化的密度计算，使用分块和采样策略"""
    # 如果数据量大，先采样用于密度计算
    if len(x_data) > sample_size:
        indices = np.random.choice(len(x_data), sample_size, replace=False)
        x_sample = x_data.iloc[indices]
        y_sample = y_data.iloc[indices]
        print(f"密度计算采样：{len(x_data):,} -> {sample_size:,} 条数据")
    else:
        x_sample = x_data
        y_sample = y_data

    # 计算密度
    xy_sample = np.vstack([x_sample, y_sample])
    kde = stats.gaussian_kde(xy_sample, bw_method="scott")  # scott方法更快

    # 对全部数据计算密度值
    xy_full = np.vstack([x_data, y_data])
    z = kde(xy_full)

    return z


def calculate_density_fast(x_data, y_data, bins=50):
    """超快速密度计算，使用直方图近似"""
    print(f"使用快速密度计算方法（{bins}x{bins} 网格）")

    # 创建2D直方图
    hist, x_edges, y_edges = np.histogram2d(
        x_data, y_data, bins=bins, range=[[0, 1], [0, 1]], density=True
    )

    # 为每个点分配密度值
    x_indices = np.digitize(x_data, x_edges) - 1
    y_indices = np.digitize(y_data, y_edges) - 1

    # 确保索引在有效范围内
    x_indices = np.clip(x_indices, 0, bins - 1)
    y_indices = np.clip(y_indices, 0, bins - 1)

    # 获取每个点的密度值
    z = hist[x_indices, y_indices]

    return z


# 并行计算评估指标
if __name__ == "__main__":
    print("开始数据处理...")

    # 创建总体进度条
    with tqdm(total=100, desc="总体进度", unit="%") as pbar:

        # 1. 并行计算评估指标 (30%)
        pbar.set_description("计算评估指标")
        with Pool(N_CORES) as pool:
            # 并行计算多个指标
            bias_result = pool.apply_async(calculate_bias, (x, y))
            mse_result = pool.apply_async(calculate_mse, (x, y))
            mae_result = pool.apply_async(calculate_mae, (x, y))
            ev_result = pool.apply_async(calculate_ev, (x, y))

            # 获取结果
            BIAS = bias_result.get()
            MSE = mse_result.get()
            MAE = mae_result.get()
            EV = ev_result.get()

        pbar.update(30)
        RMSE = np.sqrt(MSE)

        # 2. 线性回归计算 (10%)
        pbar.set_description("进行线性回归")
        slope, intercept, r_value, p_value, std_err = linregress(x, y)
        R2 = r_value**2
        regression_line = slope * x + intercept
        equation = f"y = {slope:.4f}x + {intercept:.4f}"
        pbar.update(10)

        # 3. 计算散点密度 (20%) - 多级优化
        pbar.set_description("计算散点密度（智能优化）")

        # 根据数据量和设置选择最佳密度计算方法
        data_size = len(x)
        if USE_FAST_DENSITY and data_size > 200000:
            # 超大数据量：使用直方图方法（最快）
            z = calculate_density_fast(x, y, bins=100)
        elif MEMORY_LIMIT and data_size > 50000:
            # 大数据量：使用采样KDE方法（平衡）
            z = calculate_density_optimized(x, y, DENSITY_SAMPLE_SIZE)
        else:
            # 小数据量：使用传统KDE方法（最精确）
            print("使用传统KDE方法（高精度）")
            xy = np.vstack([x, y])
            z = stats.gaussian_kde(xy)(xy)

        idx = z.argsort()
        x_sorted, y_sorted, z_sorted = x.iloc[idx], y.iloc[idx], z[idx]
        pbar.update(20)

        # 4. 创建图形和绘图 (40%)
        pbar.set_description("创建图形并绘图")
        # 降低DPI以减少内存占用，但保持高质量
        dpi_setting = 600 if MEMORY_LIMIT else 1200
        fig, ax = plt.subplots(figsize=(10, 10), dpi=dpi_setting)  # 稍微减小画布尺寸
        pbar.update(10)

        # 绘制密度散点图
        scatter = ax.scatter(
            x_sorted,
            y_sorted,
            c=z_sorted * 100,
            s=SCATTER_SIZE,
            cmap="Spectral_r",
            edgecolors="none",
        )
        pbar.update(10)

        # ✅ 精确控制色带高度与主图一致
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size="3%", pad=COLORBAR_PAD)
        cbar = plt.colorbar(scatter, cax=cax, extend="both")
        cbar.set_label(
            "Density", fontsize=COLORBAR_LABEL_SIZE, family="Times New Roman"
        )  # 色标标签字体大小
        cbar.ax.tick_params(labelsize=COLORBAR_TICK_SIZE)  # 色标刻度字体大小
        for l in cbar.ax.yaxis.get_ticklabels():
            l.set_family("Times New Roman")
        pbar.update(5)

        # 绘制1:1参考线（黑色虚线）
        ax.plot([0, 1], [0, 1], "k--", lw=1.5, label="1:1 Line")
        pbar.update(2)

        # 绘制回归线
        x_line = np.linspace(0, 1, 500)
        y_line = slope * x_line + intercept
        ax.plot(x_line, y_line, "k-", lw=1.5, label=f"Regression: {equation}", zorder=5)
        pbar.update(3)

        # 设置坐标轴范围和刻度
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_xticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        ax.tick_params(axis="both", which="major", labelsize=AXIS_TICK_SIZE)
        pbar.update(2)

        # 设置标签
        ax.set_xlabel(
            "cosi_20240428", fontsize=AXIS_LABEL_SIZE, family="Times New Roman"
        )  # X轴标签字体大小
        ax.set_ylabel(
            "NIR Reflectance", fontsize=AXIS_LABEL_SIZE, family="Times New Roman"
        )  # Y轴标签字体大小
        pbar.update(2)

        # 添加文本标注（R² 和方程）
        ax.text(
            0.1,
            0.92,
            f"$R^2 = {R2:.3f}$",
            transform=ax.transAxes,
            fontsize=TEXT_SIZE,  # R²文本字体大小
            family="Times New Roman",
            verticalalignment="top",
        )
        ax.text(
            0.1,
            0.85,
            f"{equation}",
            transform=ax.transAxes,
            fontsize=TEXT_SIZE,  # 回归方程文本字体大小
            family="Times New Roman",
            verticalalignment="top",
        )
        pbar.update(2)

        # 设置等比例坐标轴（正方形显示）
        ax.set_aspect("equal", adjustable="box")
        ax.tick_params(axis="both", labelsize=40)

        # 紧凑布局
        plt.tight_layout()

        plt.rcParams["font.family"] = "Times New Roman"
        plt.rcParams["xtick.labelsize"] = 24  # x轴刻度
        plt.rcParams["ytick.labelsize"] = 24  # y轴刻度
        plt.rcParams["axes.labelsize"] = 24  # x, y轴标题
        pbar.update(2)

        # 保存图形
        pbar.set_description("保存图形文件")
        # 根据内存限制调整保存参数
        save_dpi = 600 if MEMORY_LIMIT else 1200
        plt.savefig(
            "density_scatter_plot_20.png",
            dpi=save_dpi,
            bbox_inches="tight",
            facecolor="white",
        )
        pbar.update(2)

        pbar.set_description("完成！")

    # 显示图形
    # plt.show()
    plt.close()

    print(f"处理完成！使用了 {N_CORES} 个CPU核心")
    print(f"内存优化模式：{'开启' if MEMORY_LIMIT else '关闭'}")
    if MEMORY_LIMIT:
        print(f"图像分辨率：{save_dpi} DPI（优化模式）")
    else:
        print("图像分辨率：1200 DPI（高质量模式）")
