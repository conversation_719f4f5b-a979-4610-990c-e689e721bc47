import numpy as np
import pandas as pd

# 从analyze_wind_data_separated.py复制当前的函数
def calculate_wind_direction_class_accuracy(sim_dir, obs_dir, n_classes=16):
    """
    计算风向分类准确率（基于N个方向分类）
    """
    # 将风向转换为分类
    # 调整风向使得北向（0°）位于第一个分类的中心
    # 例如16方位时，N的范围是-11.25°到11.25°（等价于348.75°到11.25°）
    class_width = 360 / n_classes
    half_width = class_width / 2

    # 将风向调整半个分类宽度，使北向居中
    adjusted_sim = (sim_dir + half_width) % 360
    adjusted_obs = (obs_dir + half_width) % 360

    # 计算分类索引
    sim_classes = np.floor(adjusted_sim / class_width).astype(int)
    obs_classes = np.floor(adjusted_obs / class_width).astype(int)

    # 确保索引在有效范围内
    sim_classes = sim_classes % n_classes
    obs_classes = obs_classes % n_classes

    # 计算准确率
    correct = np.sum(sim_classes == obs_classes)
    total = len(sim_classes)
    class_accuracy = (correct / total) * 100 if total > 0 else 0

    return class_accuracy


def calculate_wind_direction_hit_rate(sim_dir, obs_dir, thresholds=[10, 22.5, 45]):
    """
    计算风向命中率（不同阈值下的准确率）
    """
    # 计算风向差（考虑环形特性）
    diff = sim_dir - obs_dir

    # 将差异调整到-180到180度范围
    diff = np.where(diff > 180, diff - 360, diff)
    diff = np.where(diff < -180, diff + 360, diff)

    abs_diff = np.abs(diff)

    hit_rates = {}
    for threshold in thresholds:
        # 计算在阈值内的比例
        hits = np.sum(abs_diff <= threshold)
        total = len(abs_diff)
        hit_rate = (hits / total) * 100 if total > 0 else 0
        hit_rates[threshold] = hit_rate

    return hit_rates


# 生成测试数据
print("="*80)
print("详细调试风向分类准确率问题")
print("="*80)

# 测试案例1：完全相同的数据
print("\n测试1：模拟和观测完全相同的风向")
print("-"*60)
test_dirs = np.array([0, 45, 90, 135, 180, 225, 270, 315, 10, 350])
acc_16 = calculate_wind_direction_class_accuracy(test_dirs, test_dirs, 16)
acc_8 = calculate_wind_direction_class_accuracy(test_dirs, test_dirs, 8)
print(f"16方位准确率: {acc_16:.1f}%")
print(f"8方位准确率: {acc_8:.1f}%")
print(f"预期: 两者都应该是100%")

# 测试案例2：有小偏差的数据
print("\n测试2：模拟数据有固定10度偏差")
print("-"*60)
obs_dirs = np.array([0, 45, 90, 135, 180, 225, 270, 315])
sim_dirs = (obs_dirs + 10) % 360
acc_16 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 16)
acc_8 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 8)
hit_rates = calculate_wind_direction_hit_rate(sim_dirs, obs_dirs)
print(f"观测风向: {obs_dirs}")
print(f"模拟风向: {sim_dirs}")
print(f"16方位准确率: {acc_16:.1f}%")
print(f"8方位准确率: {acc_8:.1f}%")
print(f"±10°命中率: {hit_rates[10]:.1f}%")
print(f"±22.5°命中率: {hit_rates[22.5]:.1f}%")
print(f"±45°命中率: {hit_rates[45]:.1f}%")

# 测试案例3：实际可能的情况 - 随机误差
print("\n测试3：带有随机误差的数据（标准差=15度）")
print("-"*60)
np.random.seed(123)
n = 500
obs_dirs = np.random.uniform(0, 360, n)
errors = np.random.normal(0, 15, n)
sim_dirs = (obs_dirs + errors) % 360

acc_16 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 16)
acc_8 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 8)
hit_rates = calculate_wind_direction_hit_rate(sim_dirs, obs_dirs)

print(f"样本数: {n}")
print(f"误差标准差: 15度")
print(f"16方位准确率: {acc_16:.1f}%")
print(f"8方位准确率: {acc_8:.1f}%")
print(f"±10°命中率: {hit_rates[10]:.1f}%")
print(f"±22.5°命中率: {hit_rates[22.5]:.1f}%")
print(f"±45°命中率: {hit_rates[45]:.1f}%")

if acc_8 < acc_16:
    print("⚠️ 问题：8方位准确率低于16方位！")
else:
    print("✅ 正常：8方位准确率高于16方位")

# 测试案例4：边界情况详细检查
print("\n测试4：边界附近的风向分类")
print("-"*60)

# 检查关键边界点
test_angles = [
    (11.0, "应该在N"),
    (11.5, "应该在N或NNE边界"),
    (12.0, "应该在NNE"),
    (22.0, "应该在NNE"),
    (22.5, "应该在NNE或NE边界"),
    (23.0, "应该在NE"),
    (348.0, "应该在NNW"),
    (348.75, "应该在NNW或N边界"),
    (349.0, "应该在N"),
    (359.0, "应该在N"),
]

directions_16 = ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE",
                 "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]
directions_8 = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]

for angle, description in test_angles:
    # 16方位
    adjusted = (angle + 11.25) % 360
    class_16 = int(np.floor(adjusted / 22.5)) % 16

    # 8方位
    adjusted_8 = (angle + 22.5) % 360
    class_8 = int(np.floor(adjusted_8 / 45)) % 8

    print(f"{angle:6.1f}° - {description}")
    print(f"         16方位: {directions_16[class_16]} (类别{class_16})")
    print(f"         8方位:  {directions_8[class_8]} (类别{class_8})")

# 测试案例5：查找问题原因
print("\n测试5：不同误差水平下的准确率关系")
print("-"*60)
print("误差(度) | 16方位(%) | 8方位(%) | 差值(8-16)")
print("-"*50)

problem_found = False
for error_std in [5, 10, 15, 20, 25, 30, 35, 40]:
    np.random.seed(42)
    n = 1000
    obs_dirs = np.random.uniform(0, 360, n)
    errors = np.random.normal(0, error_std, n)
    sim_dirs = (obs_dirs + errors) % 360

    acc_16 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 16)
    acc_8 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 8)
    diff = acc_8 - acc_16

    status = "❌" if acc_8 < acc_16 else "✅"
    print(f"  {error_std:3d}    | {acc_16:6.1f}   | {acc_8:6.1f}  | {diff:+6.1f} {status}")

    if acc_8 < acc_16:
        problem_found = True

if problem_found:
    print("\n⚠️ 发现问题：某些情况下8方位准确率仍低于16方位")
    print("可能的原因：")
    print("1. 数据分布不均匀，集中在某些方位边界")
    print("2. 特定的误差模式导致分类异常")
else:
    print("\n✅ 所有测试正常：8方位准确率始终高于16方位")

# 测试案例6：检查实际数据可能的问题
print("\n测试6：模拟可能的实际数据问题")
print("-"*60)

# 情况1：数据集中在某些特定角度
print("情况1：风向集中在主要方向（0,90,180,270）")
main_dirs = np.array([0, 90, 180, 270] * 25)
obs_dirs = main_dirs + np.random.normal(0, 5, 100)
sim_dirs = main_dirs + np.random.normal(0, 15, 100)
obs_dirs = obs_dirs % 360
sim_dirs = sim_dirs % 360

acc_16 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 16)
acc_8 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 8)
print(f"16方位准确率: {acc_16:.1f}%")
print(f"8方位准确率: {acc_8:.1f}%")

# 情况2：数据集中在边界附近
print("\n情况2：风向集中在8方位边界（22.5,67.5,112.5...）")
boundary_dirs = np.array([22.5, 67.5, 112.5, 157.5, 202.5, 247.5, 292.5, 337.5] * 12 + [22.5, 67.5, 112.5, 157.5])
obs_dirs = boundary_dirs + np.random.normal(0, 5, 100)
sim_dirs = boundary_dirs + np.random.normal(0, 15, 100)
obs_dirs = obs_dirs % 360
sim_dirs = sim_dirs % 360

acc_16 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 16)
acc_8 = calculate_wind_direction_class_accuracy(sim_dirs, obs_dirs, 8)
print(f"16方位准确率: {acc_16:.1f}%")
print(f"8方位准确率: {acc_8:.1f}%")

if acc_8 < acc_16:
    print("⚠️ 边界数据导致8方位准确率低于16方位！")
    print("这可能是您遇到问题的原因。")