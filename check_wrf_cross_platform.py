import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import platform
import os

# 检测操作系统
IS_WINDOWS = platform.system() == 'Windows'

# 配置 - 根据操作系统调整路径
wrf_suffix = "4"

if IS_WINDOWS:
    # Windows路径
    wrf_file = f"E:\\wrf_out{wrf_suffix}\\csv\\wrf_station_data_5min_parallel_d04.csv"
    obs_30m_file = r"F:\项目\Code\csv\202207_30M.csv"
    obs_75m_file = r"F:\项目\Code\csv\202207_75M.csv"
else:
    # Linux路径 - 请根据实际情况修改
    wrf_file = f"/home/<USER>/wrf_out{wrf_suffix}/csv/wrf_station_data_5min_parallel_d04.csv"
    obs_30m_file = "/home/<USER>/project/Code/csv/202207_30M.csv"
    obs_75m_file = "/home/<USER>/project/Code/csv/202207_75M.csv"

print("="*80)
print(f"检查 WRF_OUT{wrf_suffix} 数据")
print(f"运行环境: {platform.system()}")
print("="*80)

# 检查文件是否存在
if not os.path.exists(wrf_file):
    print(f"错误: WRF文件不存在: {wrf_file}")
    exit(1)

# 读取WRF数据
print(f"\n1. 读取WRF模拟数据...")
wrf_df = pd.read_csv(wrf_file)
print(f"   数据形状: {wrf_df.shape}")
print(f"   列名: {list(wrf_df.columns)}")

# 提取风向数据
wind_dir_30m = wrf_df['30M_30m_wind_dir_deg']
wind_dir_75m = wrf_df['75M_75m_wind_dir_deg']

print(f"\n2. 风向数据统计:")
print(f"   30M风向 - 均值: {wind_dir_30m.mean():.1f}°, 标准差: {wind_dir_30m.std():.1f}°")
print(f"   75M风向 - 均值: {wind_dir_75m.mean():.1f}°, 标准差: {wind_dir_75m.std():.1f}°")

# 检查数据是否相同
correlation = wind_dir_30m.corr(wind_dir_75m)
print(f"\n3. 30M与75M风向相关性: {correlation:.4f}")

# 计算差异
dir_diff = wind_dir_30m - wind_dir_75m
# 处理环形特性
dir_diff = np.where(dir_diff > 180, dir_diff - 360, dir_diff)
dir_diff = np.where(dir_diff < -180, dir_diff + 360, dir_diff)

print(f"\n4. 风向差异统计 (30M - 75M):")
print(f"   平均差异: {np.mean(dir_diff):.1f}°")
print(f"   差异标准差: {np.std(dir_diff):.1f}°")
print(f"   最大差异: {np.max(np.abs(dir_diff)):.1f}°")

# 检查是否完全相同
if np.array_equal(wind_dir_30m.values, wind_dir_75m.values):
    print("\n警告: 30M和75M的风向数据完全相同！")
else:
    # 统计相同值的比例
    same_count = np.sum(wind_dir_30m.values == wind_dir_75m.values)
    same_ratio = same_count / len(wind_dir_30m) * 100
    print(f"\n5. 相同值比例: {same_ratio:.1f}% ({same_count}/{len(wind_dir_30m)})")

# 检查前10个数据点
print(f"\n6. 前10个数据点对比:")
print(f"{'时间索引':<10} {'30M风向':<12} {'75M风向':<12} {'差异':<10}")
print("-"*50)
for i in range(min(10, len(wind_dir_30m))):
    diff = wind_dir_30m.iloc[i] - wind_dir_75m.iloc[i]
    if diff > 180:
        diff -= 360
    elif diff < -180:
        diff += 360
    print(f"{i:<10} {wind_dir_30m.iloc[i]:10.1f}° {wind_dir_75m.iloc[i]:10.1f}° {diff:8.1f}°")

# 读取观测数据进行对比
print("\n" + "="*80)
print("7. 读取观测数据进行对比...")

try:
    # 检查观测文件是否存在
    if not os.path.exists(obs_30m_file) or not os.path.exists(obs_75m_file):
        print("警告: 观测数据文件不存在，跳过观测数据分析")
    else:
        obs_30m = pd.read_csv(obs_30m_file)
        obs_75m = pd.read_csv(obs_75m_file)

        # 转换时间列
        wrf_df['local_time'] = pd.to_datetime(wrf_df['local_time'])
        obs_30m['Time'] = pd.to_datetime(obs_30m['Time'])
        obs_75m['Time'] = pd.to_datetime(obs_75m['Time'])

        # 合并数据
        merged_30m = pd.merge(wrf_df[['local_time', '30M_30m_wind_dir_deg']],
                              obs_30m[['Time', 'Wind_Dir']],
                              left_on='local_time', right_on='Time', how='inner')

        merged_75m = pd.merge(wrf_df[['local_time', '75M_75m_wind_dir_deg']],
                              obs_75m[['Time', 'Wind_Dir']],
                              left_on='local_time', right_on='Time', how='inner')

        print(f"   30M合并后数据点: {len(merged_30m)}")
        print(f"   75M合并后数据点: {len(merged_75m)}")

        if len(merged_30m) > 0 and len(merged_75m) > 0:
            # 计算风向差
            def calc_wind_dir_diff(sim, obs):
                diff = sim - obs
                diff = np.where(diff > 180, diff - 360, diff)
                diff = np.where(diff < -180, diff + 360, diff)
                return diff

            diff_30m = calc_wind_dir_diff(merged_30m['30M_30m_wind_dir_deg'].values,
                                          merged_30m['Wind_Dir'].values)
            diff_75m = calc_wind_dir_diff(merged_75m['75M_75m_wind_dir_deg'].values,
                                          merged_75m['Wind_Dir'].values)

            # 计算命中率
            def calc_hit_rate(diff, threshold):
                return np.sum(np.abs(diff) <= threshold) / len(diff) * 100

            print(f"\n8. 风向命中率对比:")
            for threshold in [10, 22.5, 45]:
                hr_30m = calc_hit_rate(diff_30m, threshold)
                hr_75m = calc_hit_rate(diff_75m, threshold)
                print(f"   ±{threshold}° 命中率:")
                print(f"      30M: {hr_30m:.1f}%")
                print(f"      75M: {hr_75m:.1f}%")
                if abs(hr_30m - hr_75m) < 0.5:
                    print(f"      命中率几乎相同！(差异: {abs(hr_30m - hr_75m):.2f}%)")

            # 绘制对比图
            # 设置matplotlib后端（Linux服务器可能需要）
            if not IS_WINDOWS:
                matplotlib.use('Agg')  # 无显示器环境使用

            fig, axes = plt.subplots(2, 2, figsize=(12, 10))

            # 30M风向时序图
            ax1 = axes[0, 0]
            ax1.plot(merged_30m.index[:200], merged_30m['30M_30m_wind_dir_deg'].iloc[:200],
                     'b-', alpha=0.7, label='WRF 30M')
            ax1.plot(merged_30m.index[:200], merged_30m['Wind_Dir'].iloc[:200],
                     'r-', alpha=0.7, label='Obs 30M')
            ax1.set_title(f'30M Wind Direction - WRF_OUT{wrf_suffix}')
            ax1.set_ylabel('Wind Direction (°)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 75M风向时序图
            ax2 = axes[0, 1]
            ax2.plot(merged_75m.index[:200], merged_75m['75M_75m_wind_dir_deg'].iloc[:200],
                     'b-', alpha=0.7, label='WRF 75M')
            ax2.plot(merged_75m.index[:200], merged_75m['Wind_Dir'].iloc[:200],
                     'r-', alpha=0.7, label='Obs 75M')
            ax2.set_title(f'75M Wind Direction - WRF_OUT{wrf_suffix}')
            ax2.set_ylabel('Wind Direction (°)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 风向差异分布
            ax3 = axes[1, 0]
            ax3.hist(diff_30m, bins=30, alpha=0.7, color='blue', label='30M', density=True)
            ax3.hist(diff_75m, bins=30, alpha=0.7, color='red', label='75M', density=True)
            ax3.set_title('Wind Direction Error Distribution')
            ax3.set_xlabel('Direction Error (°)')
            ax3.set_ylabel('Density')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 30M vs 75M WRF风向散点图
            ax4 = axes[1, 1]
            sample_size = min(500, len(wind_dir_30m))
            ax4.scatter(wind_dir_30m.iloc[:sample_size], wind_dir_75m.iloc[:sample_size],
                       alpha=0.5, s=10)
            ax4.plot([0, 360], [0, 360], 'r--', alpha=0.5, label='1:1 line')
            ax4.set_title(f'WRF 30M vs 75M Wind Direction (r={correlation:.3f})')
            ax4.set_xlabel('30M Wind Direction (°)')
            ax4.set_ylabel('75M Wind Direction (°)')
            ax4.set_xlim(0, 360)
            ax4.set_ylim(0, 360)
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            output_file = f'wrf_out{wrf_suffix}_wind_direction_analysis.png'
            plt.savefig(output_file, dpi=150, bbox_inches='tight')
            print(f"\n9. 分析图已保存为: {output_file}")

            # 只在有显示器的环境显示
            if IS_WINDOWS:
                plt.show()
            else:
                plt.close()  # Linux服务器关闭图形

except Exception as e:
    print(f"\n读取观测数据时出错: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*80)
print("检查完成！")
print("="*80)