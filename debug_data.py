import pandas as pd
import numpy as np

# 测试数据读取和列结构
print("=== 检查数据文件结构 ===")

# 读取模拟数据
sim_file = r"E:\wrf_out7\csv\wrf_station_data_5min_parallel_d04.csv"
print(f"\n模拟数据文件: {sim_file}")
sim_df = pd.read_csv(sim_file)
print(f"模拟数据形状: {sim_df.shape}")
print("模拟数据列名:")
for i, col in enumerate(sim_df.columns):
    print(f"  {i}: {col}")
print(f"第10列数据类型: {sim_df.iloc[:, 10].dtype}")
print(f"第10列前5个值: {sim_df.iloc[:, 10].head()}")

# 读取观测数据
obs_file = r"F:\75m通量2022.7\combined_5min_averages_parallel.xlsx"
print(f"\n观测数据文件: {obs_file}")
obs_df = pd.read_excel(obs_file)
print(f"观测数据形状: {obs_df.shape}")
print("观测数据列名:")
for i, col in enumerate(obs_df.columns):
    print(f"  {i}: {col}")
print(f"第4列数据类型: {obs_df.iloc[:, 4].dtype}")
print(f"第4列前5个值: {obs_df.iloc[:, 4].head()}")

# 检查时间列
print(f"\n模拟数据时间列 'local_time' 前5个值:")
print(sim_df['local_time'].head())
print(f"\n观测数据时间列 'Time' 前5个值:")
print(obs_df['Time'].head())