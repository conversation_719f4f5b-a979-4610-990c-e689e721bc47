#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WRF风速差值计算与可视化工具
优化版本 - 提供专业的气象数据差值分析功能

主要功能:
- WRF数据提取和处理
- 风速差值计算与统计
- 专业气象可视化
- 多种对比分析图

作者: 优化版本
日期: 2025-09-15
"""

import os
import logging
import datetime
from dataclasses import dataclass
from typing import Tuple, Optional
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib import rcParams
from netCDF4 import Dataset
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from wrf import getvar, to_np, get_cartopy, latlon_coords
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wrf_wind_diff.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 设置字体支持
plt.rcParams.update({
    'font.family': ['Times New Roman', 'SimHei'],
    'mathtext.fontset': 'stix',
    'axes.unicode_minus': False,
    'figure.max_open_warning': 0
})


@dataclass
class PlotConfig:
    """绘图配置类"""

    # 基本设置
    figure_size: Tuple[int, int] = (12, 10)
    dpi: int = 300
    font_size: int = 12
    title_size: int = 14
    label_size: int = 12

    # 风速设置
    wind_speed_max: float = 15.0
    wind_speed_min: float = 0.0
    wind_levels: int = 256

    # 差值设置
    diff_levels: int = 21
    diff_symmetric: bool = True  # 是否使用对称的差值范围

    # 颜色映射
    wind_colormap: str = 'RdYlBu_r'
    diff_colormap: str = 'RdBu_r'

    # 采样设置
    wind_arrow_step: int = 10

    # 文件输出设置
    save_bbox_inches: str = 'tight'

    # 绘图模块选择
    enable_wind_arrows: bool = True
    enable_contour_lines: bool = True
    enable_statistics: bool = True
    enable_stations: bool = True  # 是否显示气象站点

    # 气象站设置
    station_size: float = 100.0
    station_color: str = 'red'
    station_marker: str = '^'  # 三角形标记

    # 实际气象站点信息
    real_stations = [
        {
            'name': '30M',
            'lon': 104.055,
            'lat': 32.9887,
        },
        {
            'name': '75M',
            'lon': 104.016,
            'lat': 32.9998,
        }
    ]

    # 绘图范围设置
    plot_bounds = {
        'min_lon': 103.934,
        'max_lon': 104.12,
        'min_lat': 32.92,
        'max_lat': 33.07
    }

    # 是否应用范围限制
    apply_bounds: bool = True


class WRFWindAnalyzer:
    """WRF风场分析器类"""

    def __init__(self, config: PlotConfig = None):
        self.config = config or PlotConfig()

    def validate_wrf_file(self, wrf_file: str) -> bool:
        """验证WRF文件是否有效"""
        try:
            if not os.path.exists(wrf_file):
                logger.error(f"WRF文件不存在: {wrf_file}")
                return False

            with Dataset(wrf_file, 'r') as wrf:
                required_vars = ['XLONG', 'XLAT', 'U10', 'V10']
                missing_vars = [var for var in required_vars if var not in wrf.variables]

                if missing_vars:
                    logger.warning(f"WRF文件缺少变量: {missing_vars}")
                    # 尝试模式层风场
                    required_vars = ['XLONG', 'XLAT', 'U', 'V']
                    missing_vars = [var for var in required_vars if var not in wrf.variables]
                    if missing_vars:
                        logger.error(f"WRF文件缺少必要变量: {missing_vars}")
                        return False

            return True
        except Exception as e:
            logger.error(f"验证WRF文件时出错: {e}")
            return False

    def read_wrf_wind(self, filename: str, time_idx: int = 0, level: int = 0) -> Optional[dict]:
        """
        从WRF输出文件中提取风场数据

        Args:
            filename: WRF文件路径
            time_idx: 时间索引
            level: 垂直层次索引

        Returns:
            包含风场数据的字典，如果失败返回None
        """
        if not self.validate_wrf_file(filename):
            return None

        try:
            logger.info(f"开始读取WRF数据: {filename}")
            ncfile = Dataset(filename)

            # 获取投影信息
            cart_proj = get_cartopy(wrfin=ncfile)

            # 尝试读取10米风场
            try:
                u10 = getvar(ncfile, "U10", timeidx=time_idx)
                v10 = getvar(ncfile, "V10", timeidx=time_idx)
                wspd10 = np.sqrt(u10**2 + v10**2)

                # 获取经纬度坐标
                lats, lons = latlon_coords(u10)

                ncfile.close()
                logger.info("成功读取10米风场数据")

                return {
                    "u": to_np(u10),
                    "v": to_np(v10),
                    "wspd": to_np(wspd10),
                    "lat": to_np(lats),
                    "lon": to_np(lons),
                    "proj": cart_proj,
                    "height": "10m",
                }
            except:
                logger.info("未找到10米风场，尝试读取模式层风场...")

            # 读取模式层风场
            u = getvar(ncfile, "ua", timeidx=time_idx)  # 去交错的U风
            v = getvar(ncfile, "va", timeidx=time_idx)  # 去交错的V风

            # 选择特定层次
            if len(u.shape) == 3:  # 有垂直层次
                u = u[level, :, :]
                v = v[level, :, :]

            wspd = np.sqrt(u**2 + v**2)

            # 获取经纬度坐标
            lats, lons = latlon_coords(u)

            ncfile.close()
            logger.info(f"成功读取模式层{level}风场数据")

            return {
                "u": to_np(u),
                "v": to_np(v),
                "wspd": to_np(wspd),
                "lat": to_np(lats),
                "lon": to_np(lons),
                "proj": cart_proj,
                "height": f"Level {level}",
            }

        except Exception as e:
            logger.error(f"读取WRF数据时出错: {e}")
            if 'ncfile' in locals():
                ncfile.close()
            return None


    def calculate_wind_difference(self, wrf1_data: dict, wrf2_data: dict) -> dict:
        """
        计算两个WRF文件的风速差值

        Args:
            wrf1_data: 第一个WRF文件的风场数据
            wrf2_data: 第二个WRF文件的风场数据

        Returns:
            差值数据字典
        """
        try:
            diff_data = {
                "u_diff": wrf2_data["u"] - wrf1_data["u"],
                "v_diff": wrf2_data["v"] - wrf1_data["v"],
                "wspd_diff": wrf2_data["wspd"] - wrf1_data["wspd"],
                "lat": wrf1_data["lat"],
                "lon": wrf1_data["lon"],
                "proj": wrf1_data["proj"],
            }

            # 计算统计信息
            diff_data["stats"] = self._calculate_statistics(diff_data["wspd_diff"])

            return diff_data

        except Exception as e:
            logger.error(f"计算风速差值时出错: {e}")
            raise

    def _calculate_statistics(self, data: np.ndarray) -> dict:
        """计算统计信息"""
        return {
            "max": np.max(data),
            "min": np.min(data),
            "mean": np.mean(data),
            "std": np.std(data),
            "abs_max": np.max(np.abs(data))
        }


class WindDifferencePlotter:
    """风速差值绘图器类"""

    def __init__(self, config: PlotConfig = None):
        self.config = config or PlotConfig()

    def _apply_plot_bounds(self, lon: np.ndarray, lat: np.ndarray, *data_arrays) -> tuple:
        """应用绘图范围限制"""
        if not self.config.apply_bounds:
            result = [lon, lat]
            result.extend(data_arrays)
            return tuple(result)

        bounds = self.config.plot_bounds

        # 创建掩码
        lon_mask = (lon >= bounds['min_lon']) & (lon <= bounds['max_lon'])
        lat_mask = (lat >= bounds['min_lat']) & (lat <= bounds['max_lat'])
        mask = lon_mask & lat_mask

        # 找到有效区域的边界
        valid_rows, valid_cols = np.where(mask)
        if len(valid_rows) == 0:
            logger.warning("指定范围内没有有效数据点")
            result = [lon, lat]
            result.extend(data_arrays)
            return tuple(result)

        row_min, row_max = valid_rows.min(), valid_rows.max() + 1
        col_min, col_max = valid_cols.min(), valid_cols.max() + 1

        # 裁剪所有数组
        result = []
        result.append(lon[row_min:row_max, col_min:col_max])
        result.append(lat[row_min:row_max, col_min:col_max])

        for data in data_arrays:
            if data.ndim == 2:
                result.append(data[row_min:row_max, col_min:col_max])
            else:
                result.append(data)

        return tuple(result)

    def _add_weather_stations(self, ax, projection=None):
        """添加气象站点到图上"""
        if not self.config.enable_stations:
            return

        for station in self.config.real_stations:
            # 根据是否有投影选择坐标转换
            if projection:
                transform = ccrs.PlateCarree()
                ax.scatter(
                    station['lon'], station['lat'],
                    c=self.config.station_color,
                    s=self.config.station_size,
                    marker=self.config.station_marker,
                    edgecolors='white',
                    linewidth=2,
                    alpha=1.0,
                    transform=transform,
                    zorder=1000  # 确保在最上层
                )

                # 添加站点标签
                ax.text(
                    station['lon'], station['lat'],
                    f"  {station['name']}",
                    transform=transform,
                    fontsize=10,
                    color=self.config.station_color,
                    fontweight='bold',
                    ha='left',
                    va='center',
                    bbox=dict(
                        boxstyle='round,pad=0.3',
                        facecolor='white',
                        alpha=0.8,
                        edgecolor=self.config.station_color
                    ),
                    zorder=1001
                )
            else:
                # 无投影的情况
                ax.scatter(
                    station['lon'], station['lat'],
                    c=self.config.station_color,
                    s=self.config.station_size,
                    marker=self.config.station_marker,
                    edgecolors='white',
                    linewidth=2,
                    alpha=1.0,
                    zorder=1000
                )

                # 添加站点标签
                ax.annotate(
                    station['name'],
                    (station['lon'], station['lat']),
                    xytext=(5, 5),
                    textcoords='offset points',
                    fontsize=10,
                    color=self.config.station_color,
                    fontweight='bold',
                    bbox=dict(
                        boxstyle='round,pad=0.3',
                        facecolor='white',
                        alpha=0.8,
                        edgecolor=self.config.station_color
                    ),
                    zorder=1001
                )

    def _setup_figure(self, figsize: Tuple[int, int] = None, projection=None) -> Tuple[plt.Figure, plt.Axes]:
        """设置图形和坐标轴"""
        figsize = figsize or self.config.figure_size

        plt.rcParams.update({
            'figure.figsize': figsize,
            'font.size': self.config.font_size,
            'axes.titlesize': self.config.title_size,
            'axes.labelsize': self.config.label_size
        })

        if projection:
            fig = plt.figure(figsize=figsize)
            ax = plt.axes(projection=projection)
        else:
            fig, ax = plt.subplots(figsize=figsize)

        return fig, ax

    def _create_colormap(self, data: np.ndarray, symmetric: bool = True):
        """创建颜色映射"""
        if symmetric:
            vmax = np.abs(data).max()
            vmin = -vmax
        else:
            vmax = data.max()
            vmin = data.min()

        levels = np.linspace(vmin, vmax, self.config.diff_levels)

        try:
            cmap = plt.colormaps.get_cmap(self.config.diff_colormap)
        except AttributeError:
            cmap = plt.cm.get_cmap(self.config.diff_colormap)

        norm = colors.Normalize(vmin=vmin, vmax=vmax)

        return levels, cmap, norm

    def _save_figure(self, fig: plt.Figure, filename: str) -> str:
        """保存图形"""
        try:
            fig.savefig(
                filename,
                dpi=self.config.dpi,
                bbox_inches=self.config.save_bbox_inches
            )
            logger.info(f"图片已保存: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存图片时出错: {e}")
            raise
        finally:
            plt.close(fig)

    def plot_wind_difference(
        self,
        diff_data: dict,
        wrf1_file: str,
        wrf2_file: str,
        save_path: Optional[str] = None
    ) -> str:
        """
        绘制风速差值图

        Args:
            diff_data: 差值数据
            wrf1_file: 第一个文件名
            wrf2_file: 第二个文件名
            save_path: 图片保存路径

        Returns:
            保存的文件路径
        """
        try:
            logger.info("开始绘制风速差值图")

            # 应用绘图范围限制
            lon = diff_data["lon"]
            lat = diff_data["lat"]
            wspd_diff = diff_data["wspd_diff"]
            u_diff = diff_data["u_diff"]
            v_diff = diff_data["v_diff"]

            if self.config.apply_bounds:
                lon, lat, wspd_diff, u_diff, v_diff = self._apply_plot_bounds(
                    lon, lat, wspd_diff, u_diff, v_diff
                )

            # 创建图形
            fig, ax = self._setup_figure(projection=diff_data["proj"])

            # 添加地图特征
            ax.add_feature(cfeature.COASTLINE, linewidth=0.5, alpha=0.8)
            ax.add_feature(cfeature.BORDERS, linewidth=0.5, alpha=0.8)
            ax.add_feature(cfeature.STATES, linewidth=0.3, linestyle="--", alpha=0.6)

            # 设置地图范围
            if self.config.apply_bounds:
                bounds = self.config.plot_bounds
                ax.set_extent(
                    [bounds['min_lon'], bounds['max_lon'],
                     bounds['min_lat'], bounds['max_lat']],
                    crs=ccrs.PlateCarree()
                )
            else:
                ax.set_extent(
                    [lon.min(), lon.max(), lat.min(), lat.max()],
                    crs=ccrs.PlateCarree()
                )

            # 创建颜色映射
            levels, cmap, norm = self._create_colormap(wspd_diff, symmetric=self.config.diff_symmetric)

            # 绘制填充等值线
            cf = ax.contourf(
                lon, lat, wspd_diff,
                levels=levels,
                cmap=cmap,
                norm=norm,
                transform=ccrs.PlateCarree(),
                extend="both",
                alpha=0.9
            )

            # 添加等值线
            if self.config.enable_contour_lines:
                cs = ax.contour(
                    lon, lat, wspd_diff,
                    levels=levels[::2],
                    colors="black",
                    linewidths=0.8,
                    transform=ccrs.PlateCarree(),
                    alpha=0.6,
                )
                ax.clabel(cs, inline=True, fontsize=9, fmt="%.1f")

            # 绘制风矢量差值
            if self.config.enable_wind_arrows:
                # 稀疏化矢量场
                skip = self.config.wind_arrow_step
                u_diff_skip = u_diff[::skip, ::skip]
                v_diff_skip = v_diff[::skip, ::skip]
                lon_skip = lon[::skip, ::skip]
                lat_skip = lat[::skip, ::skip]

                # 绘制风矢量
                qv = ax.quiver(
                    lon_skip,
                    lat_skip,
                    u_diff_skip,
                    v_diff_skip,
                    transform=ccrs.PlateCarree(),
                    scale=120,
                    scale_units="inches",
                    width=0.003,
                    headwidth=3,
                    headlength=4,
                    color="white",
                    edgecolor="black",
                    linewidth=0.5,
                    alpha=0.8,
                )

                # 添加风矢量图例
                ax.quiverkey(
                    qv, 0.9, 0.05, 5, "5 m/s",
                    coordinates="axes",
                    fontproperties={"size": 10},
                    color="white",
                    labelcolor="black"
                )

            # 添加气象站点
            self._add_weather_stations(ax, projection=diff_data["proj"])

            # 添加颜色条
            cbar = fig.colorbar(cf, ax=ax, orientation="vertical", pad=0.02, shrink=0.8)
            cbar.set_label(
                "风速差值 (m/s)",
                rotation=270,
                labelpad=20,
                fontsize=self.config.label_size
            )
            cbar.ax.tick_params(labelsize=10)

            # 设置颜色条刻度
            max_val = diff_data["stats"]["abs_max"]
            cbar.set_ticks(np.linspace(-max_val, max_val, 9))

            # 添加网格线
            gl = ax.gridlines(
                draw_labels=True,
                dms=True,
                x_inline=False,
                y_inline=False,
                linewidth=0.5,
                linestyle="--",
                alpha=0.3
            )
            gl.top_labels = False
            gl.right_labels = False

            # 设置标题
            file1_name = os.path.basename(wrf1_file)
            file2_name = os.path.basename(wrf2_file)
            ax.set_title(
                f"风速差值分析\n({file2_name} - {file1_name})",
                fontsize=self.config.title_size,
                fontweight="bold",
                pad=20
            )

            # 添加统计信息文本框
            if self.config.enable_statistics and "stats" in diff_data:
                stats = diff_data["stats"]
                stats_text = (
                    f"最大正差值: {stats['max']:.2f} m/s\n"
                    f"最大负差值: {stats['min']:.2f} m/s\n"
                    f"平均差值: {stats['mean']:.2f} m/s\n"
                    f"标准差: {stats['std']:.2f} m/s"
                )
                ax.text(
                    0.02, 0.98, stats_text,
                    transform=ax.transAxes,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                    verticalalignment="top",
                    fontsize=10
                )

            plt.tight_layout()

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制风速差值图时出错: {e}")
            raise


    def plot_comparison(
        self,
        wrf1_data: dict,
        wrf2_data: dict,
        diff_data: dict,
        wrf1_file: str,
        wrf2_file: str,
        save_path: Optional[str] = None
    ) -> str:
        """
        绘制对比图（三个子图：WRF1、WRF2、差值）
        """
        try:
            logger.info("开始绘制对比图")

            fig, axes = plt.subplots(
                1, 3, figsize=(18, 6),
                subplot_kw={"projection": wrf1_data["proj"]}
            )

            # 应用范围限制到所有数据
            if self.config.apply_bounds:
                wrf1_lon, wrf1_lat, wrf1_wspd = self._apply_plot_bounds(
                    wrf1_data["lon"], wrf1_data["lat"], wrf1_data["wspd"]
                )
                wrf2_lon, wrf2_lat, wrf2_wspd = self._apply_plot_bounds(
                    wrf2_data["lon"], wrf2_data["lat"], wrf2_data["wspd"]
                )
                diff_lon, diff_lat, diff_wspd = self._apply_plot_bounds(
                    diff_data["lon"], diff_data["lat"], diff_data["wspd_diff"]
                )

                # 更新数据
                wrf1_data_plot = {"lon": wrf1_lon, "lat": wrf1_lat, "wspd": wrf1_wspd}
                wrf2_data_plot = {"lon": wrf2_lon, "lat": wrf2_lat, "wspd": wrf2_wspd}
                diff_data_plot = {"lon": diff_lon, "lat": diff_lat, "wspd_diff": diff_wspd}
            else:
                wrf1_data_plot = wrf1_data
                wrf2_data_plot = wrf2_data
                diff_data_plot = diff_data

            # 数据列表
            data_list = [wrf1_data_plot, wrf2_data_plot, diff_data_plot]
            file1_name = os.path.basename(wrf1_file)
            file2_name = os.path.basename(wrf2_file)
            titles = [
                f'WRF1: {file1_name}',
                f'WRF2: {file2_name}',
                "风速差值 (WRF2 - WRF1)",
            ]

            # 计算所有WRF数据的统一最大值，确保色标一致
            all_wspd_max = max(wrf1_data_plot["wspd"].max(), wrf2_data_plot["wspd"].max())

            for idx, (ax, data, title) in enumerate(zip(axes, data_list, titles)):
                # 添加地图特征
                ax.add_feature(cfeature.COASTLINE, linewidth=0.5, alpha=0.8)
                ax.add_feature(cfeature.BORDERS, linewidth=0.5, alpha=0.8)
                ax.add_feature(cfeature.STATES, linewidth=0.3, linestyle="--", alpha=0.6)

                # 设置地图范围
                if self.config.apply_bounds:
                    bounds = self.config.plot_bounds
                    ax.set_extent(
                        [bounds['min_lon'], bounds['max_lon'],
                         bounds['min_lat'], bounds['max_lat']],
                        crs=ccrs.PlateCarree()
                    )
                else:
                    ax.set_extent(
                        [data["lon"].min(), data["lon"].max(),
                         data["lat"].min(), data["lat"].max()],
                        crs=ccrs.PlateCarree()
                    )

                # 选择要绘制的数据和颜色映射
                if idx < 2:  # WRF原始数据
                    plot_data = data["wspd"]
                    # 使用统一的最大值创建色标，避免突变
                    vmin = 0
                    vmax = all_wspd_max * 1.05  # 稍微扩大范围，避免边界问题
                    levels = np.linspace(vmin, vmax, 20)
                    try:
                        cmap = plt.colormaps.get_cmap(self.config.wind_colormap)
                    except AttributeError:
                        cmap = plt.cm.get_cmap(self.config.wind_colormap)
                    norm = colors.Normalize(vmin=vmin, vmax=vmax)
                    label = "风速 (m/s)"
                else:  # 差值数据
                    plot_data = data["wspd_diff"]
                    try:
                        cmap = plt.colormaps.get_cmap(self.config.diff_colormap)
                    except AttributeError:
                        cmap = plt.cm.get_cmap(self.config.diff_colormap)
                    vmax = np.abs(plot_data).max()
                    vmin = -vmax
                    label = "风速差值 (m/s)"
                    levels = np.linspace(vmin, vmax, self.config.diff_levels)
                    norm = colors.Normalize(vmin=vmin, vmax=vmax)

                # 绘制填充等值线
                cf = ax.contourf(
                    data["lon"],
                    data["lat"],
                    plot_data,
                    levels=levels,
                    cmap=cmap,
                    norm=norm,
                    transform=ccrs.PlateCarree(),
                    extend="both",
                    alpha=0.9
                )

                # 添加等值线
                if self.config.enable_contour_lines:
                    cs = ax.contour(
                        data["lon"],
                        data["lat"],
                        plot_data,
                        levels=levels[::3],
                        colors="black",
                        linewidths=0.5,
                        transform=ccrs.PlateCarree(),
                        alpha=0.4
                    )
                    ax.clabel(cs, inline=True, fontsize=8, fmt="%.1f")

                # 添加气象站点
                self._add_weather_stations(ax, projection=wrf1_data["proj"])

                # 添加颜色条
                cbar = plt.colorbar(
                    cf, ax=ax, orientation="horizontal",
                    pad=0.1, shrink=0.8, aspect=20
                )
                cbar.set_label(label, fontsize=10)
                cbar.ax.tick_params(labelsize=9)

                # 设置颜色条刻度
                if idx < 2:  # WRF原始数据使用相同的刻度
                    # 设置统一的刻度，从0到最大值
                    n_ticks = 11  # 显示11个刻度
                    tick_values = np.linspace(0, vmax, n_ticks)
                    cbar.set_ticks(tick_values)
                    cbar.set_ticklabels([f'{v:.1f}' for v in tick_values])

                # 添加网格线
                gl = ax.gridlines(
                    draw_labels=True,
                    dms=True,
                    x_inline=False,
                    y_inline=False,
                    linewidth=0.5,
                    linestyle="--",
                    alpha=0.3
                )
                gl.top_labels = False
                gl.right_labels = False

                # 设置标题
                ax.set_title(title, fontsize=11, fontweight="bold", pad=10)

            # 添加总标题
            fig.suptitle(
                "WRF风场对比分析",
                fontsize=self.config.title_size,
                fontweight="bold",
                y=1.02
            )

            # 调整布局
            plt.tight_layout()

            # 保存或显示图形
            if save_path:
                return self._save_figure(fig, save_path)
            else:
                plt.show()
                return ""

        except Exception as e:
            logger.error(f"绘制对比图时出错: {e}")
            raise


def main():
    """
    主函数 - WRF风速差值分析
    """
    try:
        # === 路径配置 ===
        wrf1_file = r"E:\wrf_out7\wrfout_d04_2022-07-01_14_00_00"  # WRF文件1
        wrf2_file = r"E:\wrf_out5\wrfout_d04_2022-07-01_14_00_00"  # WRF文件2

        # === 参数配置 ===
        time_idx = 0  # 时间索引
        level = 0     # 垂直层次（0为最低层）

        # === 创建配置 ===
        plot_config = PlotConfig(
            figure_size=(12, 10),
            dpi=300,
            wind_speed_max=15.0,
            wind_speed_min=0.0,
            diff_levels=21,
            diff_symmetric=True,
            wind_colormap='RdYlBu_r',
            diff_colormap='RdBu_r',
            wind_arrow_step=10,
            enable_wind_arrows=True,
            enable_contour_lines=True,
            enable_statistics=True,
            enable_stations=True,  # 启用气象站点显示
            apply_bounds=True,  # 启用范围限制
            station_size=100.0,
            station_color='red',
            station_marker='^'
        )

        # === 创建分析器和绘图器 ===
        analyzer = WRFWindAnalyzer(plot_config)
        plotter = WindDifferencePlotter(plot_config)

        print("=" * 60)
        print("WRF风速差值分析")
        print("=" * 60)
        print(f"WRF文件1: {os.path.basename(wrf1_file)}")
        print(f"WRF文件2: {os.path.basename(wrf2_file)}")
        print(f"时间索引: {time_idx}")
        print(f"垂直层次: {level}")
        print("=" * 60)

        # === 读取WRF数据 ===
        print("\n1. 读取WRF数据...")
        wrf1_data = analyzer.read_wrf_wind(wrf1_file, time_idx, level)
        if not wrf1_data:
            print("   ✗ 读取WRF1文件失败")
            return
        print(f"   ✓ 成功读取WRF1数据 (高度: {wrf1_data['height']})")

        wrf2_data = analyzer.read_wrf_wind(wrf2_file, time_idx, level)
        if not wrf2_data:
            print("   ✗ 读取WRF2文件失败")
            return
        print(f"   ✓ 成功读取WRF2数据 (高度: {wrf2_data['height']})")

        # === 计算风速差值 ===
        print("\n2. 计算风速差值...")
        diff_data = analyzer.calculate_wind_difference(wrf1_data, wrf2_data)
        print("   ✓ 差值计算完成")

        # === 输出统计信息 ===
        print("\n3. 风速差值统计:")
        stats = diff_data['stats']
        print(f"   最大正差值: {stats['max']:.2f} m/s")
        print(f"   最大负差值: {stats['min']:.2f} m/s")
        print(f"   平均差值: {stats['mean']:.2f} m/s")
        print(f"   标准差: {stats['std']:.2f} m/s")

        # === 绘制图形 ===
        print("\n4. 绘制风速差值图...")
        output_file = plotter.plot_wind_difference(
            diff_data,
            wrf1_file,
            wrf2_file,
            save_path="wind_difference.png"
        )
        if output_file:
            print(f"   ✓ 风速差值图已保存: {output_file}")

        print("\n5. 绘制对比图...")
        output_file = plotter.plot_comparison(
            wrf1_data,
            wrf2_data,
            diff_data,
            wrf1_file,
            wrf2_file,
            save_path="wind_comparison.png"
        )
        if output_file:
            print(f"   ✓ 对比图已保存: {output_file}")

        print("\n" + "=" * 60)
        print("分析完成！")
        print("=" * 60)

    except Exception as e:
        logger.error(f"主程序执行出错: {e}")
        print(f"\n✗ 程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
